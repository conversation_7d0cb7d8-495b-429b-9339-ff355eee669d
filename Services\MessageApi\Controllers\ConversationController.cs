﻿using MessageBusinessLayer;
using MessageContracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

namespace MessageApi.Controllers
{

    [Route("api/[controller]")]
    [ApiController]
    public class ConversationController: ControllerBase
    {
        private readonly ILogger<ConversationController> _logger;
        private readonly IStringLocalizer<ConversationController> _localizer;
        private readonly IConversationQueryHandler<Conversation>  _conversationQueryHandler;
        private readonly IConversationsCommandHandler<Conversation> _converasationCommandHandler; 
        public ConversationController(ILogger<ConversationController> logger,IStringLocalizer<ConversationController> localizer,IConversationQueryHandler<Conversation> conversationQueryHandler,IConversationsCommandHandler<Conversation> conversationsCommandHandler)
        {
            _logger = logger;
            _localizer = localizer;
            _conversationQueryHandler = conversationQueryHandler;
            _converasationCommandHandler = conversationsCommandHandler;
        }

        [HttpGet("user/{userId}")]
        public async Task<IActionResult> GetUserConversations(Guid userId, int page = 1, int pageSize = 20)
        {
            try
            {
                var conversations = await _conversationQueryHandler.GetUserConversationsAsync(userId, page, pageSize);
                return Ok(conversations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching conversations for user {UserId}", userId);
                return StatusCode(500, "An error occurred while fetching conversations.");
            }
        }

        [HttpPost("create")]
        public async Task<IActionResult> CreateConversation([FromBody] Conversation conversationModel, [FromQuery] Guid creatorUserId)
        {
            try
            {
                var conversation = await _converasationCommandHandler.CreateConversationAsync(conversationModel, creatorUserId);
                if (conversation == null)
                {
                    _logger.LogWarning("Failed to create conversation for creator {CreatorUserId}", creatorUserId);
                    return BadRequest("Failed to create conversation");
                }
                return Ok(conversation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating conversation for creator {CreatorUserId}", creatorUserId);
                return StatusCode(500, "An error occurred while creating the conversation.");
            }
        }

        [HttpPost("{conversationId}/addParticipants")]
        public async Task<IActionResult> AddParticipants(string conversationId, [FromBody] Conversation conversationModel, [FromQuery] Guid addedByUserId)
        {
            try
            {
                var updatedConversation = await _converasationCommandHandler.AddParticipantsToExistingConversationAsync(conversationId, conversationModel, addedByUserId);
                if (updatedConversation == null)
                {
                    _logger.LogWarning("Failed to add participants to conversation {ConversationId}", conversationId);
                    return BadRequest("Failed to add participants");
                }
                return Ok(updatedConversation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding participants to conversation {ConversationId}", conversationId);
                return StatusCode(500, "An error occurred while adding participants.");
            }
        }

        [HttpPost("{conversationId}/removeParticipants")]
        public async Task<IActionResult> RemoveParticipants(string conversationId, [FromBody] IEnumerable<Guid> userIds, [FromQuery] Guid removedByUserId)
        {
            try
            {
                await _converasationCommandHandler.RemoveParticipantsAsync(Guid.Parse(conversationId), userIds, removedByUserId);
                return Ok(new { Message = "Participants removal process completed" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing participants from conversation {ConversationId}", conversationId);
                return StatusCode(500, "An error occurred while removing participants.");
            }
        }

        [HttpPut("{conversationId}/updateName")]
        public async Task<IActionResult> UpdateConversationName(string conversationId, [FromBody] string newName, [FromQuery] Guid updatedByUserId)
        {
            try
            {
                await _converasationCommandHandler.UpdateConversationNameAsync(Guid.Parse(conversationId), newName, updatedByUserId);
                return Ok(new { Message = "Conversation name update process completed" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating conversation {ConversationId} name", conversationId);
                return StatusCode(500, "An error occurred while updating conversation name.");
            }
        }

    }
}
