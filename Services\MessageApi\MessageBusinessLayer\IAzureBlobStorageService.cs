﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MessageBusinessLayer
{
    public interface IAzureBlobStorageService<T>
    {
        Task<string> UploadAttachmentAsync(byte[] content, string fileName, string contentType);
        Task<byte[]> DownloadAttachmentAsync(string blobFileName);
        Task<bool> DeleteAttachmentAsync(string blobFileName);
        Task<bool> AttachmentExistsAsync(string blobFileName);
        string GetAttachmentUrl(string blobFileName);
    }
}
