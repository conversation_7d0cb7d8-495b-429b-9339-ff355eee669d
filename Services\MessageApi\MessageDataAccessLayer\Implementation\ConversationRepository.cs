﻿using MessageContracts;
using DataAccessLayer.Context;

namespace MessageDataAccessLayer.Implementation
{
    public class ConversationRepository : GenericRepository<Conversation>, IConversationRepository
    {
        private readonly MessageApiDatabaseContext _context;
        public ConversationRepository(MessageApiDatabaseContext context) : base(context)
        {
            _context = context;
        }

    }
}
