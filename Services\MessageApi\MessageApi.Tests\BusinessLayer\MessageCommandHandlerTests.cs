using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using MessageContracts;
using MessageBusinessLayer.CommandHandler;
using MessageBusinessLayer.Services;
using MessageDataAccessLayer.Implementation;
using System.Linq;

namespace MessageApi.Tests.BusinessLayer
{
    [TestFixture]
    public class MessageCommandHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<MessageCommandHandler>> _loggerMock;
        private Mock<IAzureCommunicationService> _azureCommunicationServiceMock;
        private Mock<IAttachmentService> _attachmentServiceMock;
        private Mock<IMessageNotificationService> _notificationServiceMock;
        private Mock<IMessageRepository> _messageRepositoryMock;
        private MessageCommandHandler _commandHandler;
        private List<Message> _testMessages;
        private List<MessageAttachment> _testAttachments;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<MessageCommandHandler>>();
            _azureCommunicationServiceMock = new Mock<IAzureCommunicationService>();
            _attachmentServiceMock = new Mock<IAttachmentService>();
            _notificationServiceMock = new Mock<IMessageNotificationService>();
            _messageRepositoryMock = new Mock<IMessageRepository>();

            // Setup unit of work to return message repository
            _unitOfWorkMock.Setup(x => x.MessageRepository).Returns(_messageRepositoryMock.Object);

            _testMessages = new List<Message>
            {
                new Message
                {
                    MessageId = Guid.NewGuid(),
                    SenderName = "John Doe",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = "Jane Doe",
                    ReceiverEmailId = "<EMAIL>",
                    Subject = "Test Subject",
                    MessageContent = "Test Content",
                    SentDateTime = DateTime.UtcNow,
                    Status = 1,
                    IsDeleted = false,
                    IsArchived = false,
                    HasAttachments = false,
                    AttachmentCount = 0
                }
            };

            _testAttachments = new List<MessageAttachment>
            {
                new MessageAttachment
                {
                    AttachmentId = Guid.NewGuid(),
                    MessageId = _testMessages[0].MessageId,
                    FileName = "test.pdf",
                    OriginalFileName = "test.pdf",
                    ContentType = "application/pdf",
                    FileSizeBytes = 1024,
                    BlobFileName = "blob_test.pdf",
                    BlobStorageUrl = "https://test.blob.core.windows.net/attachments/blob_test.pdf",
                    BlobContainerName = "attachments",
                    FileHash = "testhash123",
                    UploadedDateTime = DateTime.UtcNow,
                    IsScanned = true,
                    IsSafe = true
                }
            };

            _commandHandler = new MessageCommandHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _azureCommunicationServiceMock.Object,
                _attachmentServiceMock.Object,
                _notificationServiceMock.Object
            );
        }

        #region SendMessage Tests

        [Test]
        public async Task SendMessage_ValidMessage_ReturnsMessageIdAndLogsSuccess()
        {
            // Arrange
            var message = _testMessages[0];
            var azureMessageId = "azure-msg-123";
            var conversationId = "conv-123";

            _azureCommunicationServiceMock.Setup(x => x.SendMessageAsync(
                message.SenderEmailId, message.ReceiverEmailId, message.MessageContent))
                .ReturnsAsync(azureMessageId);

            _azureCommunicationServiceMock.Setup(x => x.GetOrCreateConversationAsync(
                message.SenderEmailId, message.ReceiverEmailId))
                .ReturnsAsync(conversationId);

            _messageRepositoryMock.Setup(x => x.AddAsync(It.IsAny<List<Message>>()))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(x => x.SaveAsync())
                .ReturnsAsync(1);

            // Act
            var result = await _commandHandler.SendMessage(message, null);

            // Assert
            result.Should().NotBeEmpty();
            message.AzureMessageId.Should().Be(azureMessageId);
            message.ConversationId.Should().Be(conversationId);
            message.Status.Should().Be(1);

            _messageRepositoryMock.Verify(x => x.AddAsync(It.IsAny<List<Message>>()), Times.Once);
            _unitOfWorkMock.Verify(x => x.SaveAsync(), Times.Once);

            // Verify logger was called for successful operation (following TeyaHealth patterns)
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Message sent successfully")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.AtLeastOnce);
        }

        [Test]
        public async Task SendMessage_WithAttachments_ProcessesAttachmentsCorrectly()
        {
            // Arrange
            var message = _testMessages[0];
            var attachmentRequests = new List<AttachmentRequest>
            {
                new AttachmentRequest
                {
                    FileName = "test.pdf",
                    ContentType = "application/pdf",
                    FileContentBase64 = Convert.ToBase64String(new byte[] { 1, 2, 3, 4, 5 })
                }
            };

            _attachmentServiceMock.Setup(x => x.ProcessAttachmentsAsync(It.IsAny<Guid>(), attachmentRequests))
                .ReturnsAsync(_testAttachments);

            _azureCommunicationServiceMock.Setup(x => x.SendMessageAsync(
                It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync("azure-msg-123");

            _azureCommunicationServiceMock.Setup(x => x.GetOrCreateConversationAsync(
                It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync("conv-123");

            _messageRepositoryMock.Setup(x => x.AddAsync(It.IsAny<List<Message>>()))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(x => x.SaveAsync())
                .ReturnsAsync(1);

            // Act
            var result = await _commandHandler.SendMessage(message, attachmentRequests);

            // Assert
            result.Should().NotBeEmpty();
            message.HasAttachments.Should().BeTrue();
            message.AttachmentCount.Should().Be(1);
            message.Attachments.Should().HaveCount(1);

            _attachmentServiceMock.Verify(x => x.ProcessAttachmentsAsync(It.IsAny<Guid>(), attachmentRequests), Times.Once);
        }

        [Test]
        public async Task SendMessage_ExceptionThrown_ThrowsException()
        {
            // Arrange
            var message = _testMessages[0];
            _azureCommunicationServiceMock.Setup(x => x.SendMessageAsync(
                It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ThrowsAsync(new Exception("Azure service error"));

            // Act & Assert
            await FluentActions.Invoking(() => _commandHandler.SendMessage(message, null))
                .Should().ThrowAsync<Exception>()
                .WithMessage("Azure service error");
        }

        #endregion

        #region MarkMessageAsRead Tests

        [Test]
        public async Task MarkMessageAsRead_ValidMessageId_UpdatesMessageAndLogsSuccess()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            var message = _testMessages[0];
            message.MessageId = messageId;

            _messageRepositoryMock.Setup(x => x.GetByIdAsync(messageId))
                .ReturnsAsync(message);

            _messageRepositoryMock.Setup(x => x.UpdateAsync(It.IsAny<Message>()))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(x => x.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _commandHandler.MarkMessageAsRead(messageId);

            // Assert
            message.ReadDateTime.Should().NotBeNull();
            message.ReadDateTime.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));

            _messageRepositoryMock.Verify(x => x.UpdateAsync(message), Times.Once);
            _unitOfWorkMock.Verify(x => x.SaveAsync(), Times.Once);

            // Verify logger was called for successful operation (following TeyaHealth patterns)
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Message marked as read")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Test]
        public async Task MarkMessageAsRead_MessageNotFound_ThrowsException()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            _messageRepositoryMock.Setup(x => x.GetByIdAsync(messageId))
                .ReturnsAsync((Message)null);

            // Act & Assert
            await FluentActions.Invoking(() => _commandHandler.MarkMessageAsRead(messageId))
                .Should().ThrowAsync<ArgumentException>()
                .WithMessage($"Message not found: {messageId}");
        }

        #endregion

        #region SoftDeleteMessage Tests

        [Test]
        public async Task SoftDeleteMessage_ValidMessageId_SoftDeletesMessage()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            var deletedBy = "<EMAIL>";
            var reason = "Test deletion";
            var message = _testMessages[0];
            message.MessageId = messageId;

            _messageRepositoryMock.Setup(x => x.GetByIdAsync(messageId))
                .ReturnsAsync(message);

            _messageRepositoryMock.Setup(x => x.UpdateAsync(It.IsAny<Message>()))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(x => x.SaveAsync())
                .ReturnsAsync(1);

            // Act
            var result = await _commandHandler.SoftDeleteMessage(messageId, deletedBy, reason);

            // Assert
            result.Should().BeTrue();
            message.IsDeleted.Should().BeTrue();
            message.DeletedBy.Should().Be(deletedBy);
            message.DeletedDateTime.Should().NotBeNull();
            message.DeletedDateTime.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));

            _messageRepositoryMock.Verify(x => x.UpdateAsync(message), Times.Once);
            _unitOfWorkMock.Verify(x => x.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task SoftDeleteMessage_MessageNotFound_ReturnsFalse()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            _messageRepositoryMock.Setup(x => x.GetByIdAsync(messageId))
                .ReturnsAsync((Message)null);

            // Act
            var result = await _commandHandler.SoftDeleteMessage(messageId, "<EMAIL>");

            // Assert
            result.Should().BeFalse();
        }

        #endregion

        #region ArchiveMessage Tests

        [Test]
        public async Task ArchiveMessage_ValidMessageId_ArchivesMessage()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            var archivedBy = "<EMAIL>";
            var message = _testMessages[0];
            message.MessageId = messageId;

            _messageRepositoryMock.Setup(x => x.GetByIdAsync(messageId))
                .ReturnsAsync(message);

            _messageRepositoryMock.Setup(x => x.UpdateAsync(It.IsAny<Message>()))
                .Returns(Task.CompletedTask);

            _unitOfWorkMock.Setup(x => x.SaveAsync())
                .ReturnsAsync(1);

            // Act
            var result = await _commandHandler.ArchiveMessage(messageId, archivedBy);

            // Assert
            result.Should().BeTrue();
            message.IsArchived.Should().BeTrue();
            message.ArchivedBy.Should().Be(archivedBy);
            message.ArchivedDateTime.Should().NotBeNull();
            message.ArchivedDateTime.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));

            _messageRepositoryMock.Verify(x => x.UpdateAsync(message), Times.Once);
            _unitOfWorkMock.Verify(x => x.SaveAsync(), Times.Once);
        }

        #endregion
    }
}
