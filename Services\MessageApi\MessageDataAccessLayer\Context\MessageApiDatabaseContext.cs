﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using MessageContracts;
using Container = Microsoft.Azure.Cosmos.Container;
using System;

namespace DataAccessLayer.Context
{
    public class MessageApiDatabaseContext : DbContext
    {
        private readonly IStringLocalizer<MessageApiDatabaseContext> _localizer;
        private readonly ILogger<MessageApiDatabaseContext> _logger;

        public MessageApiDatabaseContext(
            DbContextOptions<MessageApiDatabaseContext> options,
            IStringLocalizer<MessageApiDatabaseContext> localizer,
            ILogger<MessageApiDatabaseContext> logger)
            : base(options)
        {
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        // Cosmos-backed DbSets (each maps to its own container)
        public DbSet<Message> Messages { get; set; }
        public DbSet<MessageAttachment> MessageAttachments { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<Conversation> Conversations { get; set; }
        public DbSet<ConversationParticipant> ConversationParticipants { get; set; }
        public DbSet<MessageReceipt> MessageReceipts { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            try
            {

                modelBuilder.Entity<User>(entity =>
                {
                    entity.ToTable("Users", "MessageService");
                    entity.HasKey(e => e.UserId);
                });


                modelBuilder.Entity<Conversation>(entity =>
                {
                    entity.ToTable("Conversations", "MessageService");
                    entity.HasKey(e => e.ConversationId);
                });
                   

                modelBuilder.Entity<Message>(entity =>
                {
                    entity.ToTable("Messages", "MessageService");
                    entity.HasKey(e => e.MessageId);
                });

                modelBuilder.Entity<MessageAttachment>(entity =>
                {
                    entity.ToTable("MessageAttachments", "MessageService");
                    entity.HasKey(e => e.AttachmentId);
                });

                modelBuilder.Entity<MessageReceipt>(
                    enttity =>
                    {
                        enttity.ToTable("MessageReceipts", "MessageService");
                        enttity.HasKey(e => new { e.MessageId, e.UserId });
                    });
                    
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DatabaseError"]);
            }
        }
    }
}
