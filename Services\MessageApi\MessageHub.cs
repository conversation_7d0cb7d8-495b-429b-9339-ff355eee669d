using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using MessageContracts.DTOs;
using MessageContracts;
using MessageBusinessLayer;
using System;
using System.Collections.Concurrent;
using System.Threading.Tasks;
using System.Linq;
using System.Collections.Generic;

namespace MessageApi
{
    public class MessageHub : Hub
    {
        private readonly ILogger<MessageHub> _logger;
        private readonly IMessageCommandHandler<Message> _messageCommandHandler;
        private readonly IConversationQueryHandler<Conversation> _conversationQueryHandler;
        private readonly IAzureBlobStorageService<MessageAttachment> _blobStorageService;

        // Store user connections and their conversation memberships
        private static readonly ConcurrentDictionary<string, Guid> _connectionUserMap = new();
        private static readonly ConcurrentDictionary<Guid, HashSet<string>> _userConnections = new();
        private static readonly ConcurrentDictionary<Guid, DateTime> _userLastSeen = new();

        public MessageHub(
            ILogger<MessageHub> logger,
            IMessageCommandHandler<Message> messageCommandHandler,
            IConversationQueryHandler<Conversation> conversationQueryHandler,
            IAzureBlobStorageService<MessageAttachment> blobStorageService)
        {
            _logger = logger;
            _messageCommandHandler = messageCommandHandler;
            _conversationQueryHandler = conversationQueryHandler;
            _blobStorageService = blobStorageService;
        }

        public override async Task OnConnectedAsync()
        {
            _logger.LogInformation("User connected: {ConnectionId}", Context.ConnectionId);
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception exception)
        {
            var connectionId = Context.ConnectionId;

            if (_connectionUserMap.TryRemove(connectionId, out var userId))
            {
                // Update user's last seen
                _userLastSeen[userId] = DateTime.UtcNow;

                // Remove connection from user's connection list
                if (_userConnections.TryGetValue(userId, out var connections))
                {
                    connections.Remove(connectionId);
                    if (connections.Count == 0)
                    {
                        _userConnections.TryRemove(userId, out _);

                        // Notify user's conversations that they went offline
                        await NotifyUserStatusChange(userId, false);
                    }
                }
            }

            _logger.LogInformation("User disconnected: {ConnectionId}", connectionId);
            await base.OnDisconnectedAsync(exception);
        }

        /// <summary>
        /// Authenticate user and join their conversations
        /// </summary>
        public async Task AuthenticateUser(Guid userId)
        {
            var connectionId = Context.ConnectionId;

            // Map connection to user
            _connectionUserMap[connectionId] = userId;

            // Add connection to user's connection list
            _userConnections.AddOrUpdate(userId,
                new HashSet<string> { connectionId },
                (key, existing) => { existing.Add(connectionId); return existing; });

            // Join user to their conversation groups
            var userConversations = await _conversationQueryHandler.GetUserConversationsAsync(userId);
            foreach (var conversation in userConversations)
            {
                await Groups.AddToGroupAsync(connectionId, conversation.ConversationId);
            }

            // Notify user's conversations that they came online
            await NotifyUserStatusChange(userId, true);

            _logger.LogInformation("User {UserId} authenticated with connection {ConnectionId}", userId, connectionId);
        }
