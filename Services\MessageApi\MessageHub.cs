using Microsoft.AspNetCore.SignalR;
using System;
using System.Threading.Tasks;

namespace MessageApi
{
    public class MessageHub : Hub
    {
        public override async Task OnConnectedAsync()
        {
            // Add user to a group for each conversation they are a part of
            // This will be implemented later
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception exception)
        {
            // Remove user from conversation groups
            // This will be implemented later
            await base.OnDisconnectedAsync(exception);
        }

        public async Task JoinConversation(string conversationId)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, conversationId);
        }

        public async Task LeaveConversation(string conversationId)
        { 
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, conversationId);
        }
    }
}
