﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DataAccessLayer.Context;

namespace MessageDataAccessLayer.Implementation
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly MessageApiDatabaseContext _context;

        public IMessageRepository MessageRepository { get; }
        public IAttachmentRepository AttachmentRepository { get; }
        public IUserRepository UserRepository { get; }
        public IConversationRepository ConversationRepository { get; }
        public IMessageReceiptsRepository MessageReceiptsRepository { get; }


        public UnitOfWork(MessageApiDatabaseContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            MessageRepository = new MessageRepository(_context);
            AttachmentRepository = new AttachmentRepository(_context);
            UserRepository = new UserRepository(_context);
            ConversationRepository = new ConversationRepository(_context);
            MessageReceiptsRepository = new MessageReceiptsRepository(_context);
        }

        public async Task<int> SaveAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
