﻿using Azure.Communication.Identity;
using MessageContracts;
using MessageDataAccessLayer.Implementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MessageBusinessLayer.CommandHandler
{
    public class UsersCommandHandler : IUserCommandHandler<User>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<UsersCommandHandler> _logger;
        private readonly CommunicationIdentityClient _identityClient;
        private readonly string _connectionString;

        public UsersCommandHandler(
            IConfiguration configuration,
            IUnitOfWork unitOfWork,
            ILogger<UsersCommandHandler> logger)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;

            _connectionString = Environment.GetEnvironmentVariable("AzureCommunicationServices__ConnectionString");
            if (string.IsNullOrEmpty(_connectionString))
            {
                _logger.LogError("Azure Communication Services connection string not found.");
                throw new InvalidOperationException("Azure Communication Services connection string is not configured.");
            }

            try
            {
                _identityClient = new CommunicationIdentityClient(_connectionString);
                _logger.LogInformation("ACS Identity client initialized for user management");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize ACS Identity client for user management");
                throw;
            }
        }

        public async Task CreateAcsUserAsync(User user)
        {
            try
            {
                // Validate input user
                if (user == null)
                {
                    throw new ArgumentNullException(nameof(user));
                }

                // Create ACS user identity
                var response = await _identityClient.CreateUserAsync();
                var acsUserId = response.Value.Id;

                _logger.LogInformation("Created new ACS user with ID: {AcsUserId}", acsUserId);

                // Update user with ACS ID
                user.AcsUserId = acsUserId;

                // Add user to repository
                await _unitOfWork.UserRepository.AddAsync((IEnumerable<User>)user);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Successfully created and saved user {UserId} with ACS ID {AcsUserId}",
                    user.UserId, acsUserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create ACS user for user {UserId}", user?.UserId);
                throw;
            }
        }

        public async Task<(string Token, DateTimeOffset ExpiresOn)> GetUserTokenAsync(string acsUserId)
        {
            try
            {
                if (string.IsNullOrEmpty(acsUserId))
                {
                    throw new ArgumentException("ACS User ID cannot be null or empty", nameof(acsUserId));
                }

                var tokenResponse = await _identityClient.GetTokenAsync(
                    new Azure.Communication.CommunicationUserIdentifier(acsUserId),
                    new[] { CommunicationTokenScope.Chat }
                );

                _logger.LogInformation("Generated token for ACS user {AcsUserId}", acsUserId);
                return (tokenResponse.Value.Token, tokenResponse.Value.ExpiresOn);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get token for ACS user {AcsUserId}", acsUserId);
                throw;
            }
        }

        public async Task DeleteAcsUserAsync(User user)
        {
            try
            {
                string acsUserId = user.AcsUserId;
                if (string.IsNullOrEmpty(acsUserId))
                {
                    throw new ArgumentException("ACS User ID cannot be null or empty", nameof(acsUserId));
                }

                await _identityClient.DeleteUserAsync(new Azure.Communication.CommunicationUserIdentifier(acsUserId));
                user.IsActive = false;
                await _unitOfWork.UserRepository.UpdateAsync(user);

                _logger.LogInformation("Deleted ACS user {AcsUserId}", acsUserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete ACS user {AcsUserId}");
                throw;
            }
        }
    }
}