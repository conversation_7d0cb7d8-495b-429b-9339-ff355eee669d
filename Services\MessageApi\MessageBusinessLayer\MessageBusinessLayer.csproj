﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <Version>1.0.50704.2</Version>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Contracts\MessageContracts.csproj" />
    <ProjectReference Include="..\MessageDataAccessLayer\MessageDataAccessLayer.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Data.Tables" Version="12.10.0" />
    <PackageReference Include="Microsoft.Azure.Cosmos" Version="3.53.0" />
    <PackageReference Include="Microsoft.Azure.Cosmos.Table" Version="1.0.8" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.4" />
    <PackageReference Include="Azure.Communication.Chat" Version="1.3.0" />
    <PackageReference Include="Azure.Communication.Identity" Version="1.3.1" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.22.1" />
  </ItemGroup>

</Project>
