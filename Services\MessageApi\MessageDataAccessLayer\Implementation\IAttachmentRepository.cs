using MessageContracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MessageDataAccessLayer.Implementation
{
    public interface IAttachmentRepository : IGenericRepository<MessageAttachment>
    {
        Task<List<MessageAttachment>> GetAttachmentsByMessageIdAsync(Guid messageId);
        Task<MessageAttachment?> GetAttachmentByFileNameAsync(string fileName);
        Task<List<MessageAttachment>> GetAttachmentsByContentTypeAsync(string contentType);
        Task<long> GetTotalAttachmentSizeByMessageIdAsync(Guid messageId);
    }
}
