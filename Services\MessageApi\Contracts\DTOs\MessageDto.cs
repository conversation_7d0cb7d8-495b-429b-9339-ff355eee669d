using System;
using System.Collections.Generic;

namespace MessageContracts.DTOs
{
    /// <summary>
    /// Lightweight DTO for real-time message transmission
    /// </summary>
    public class MessageDto
    {
        public Guid MessageId { get; set; }
        public Guid ConversationId { get; set; }
        public Guid SenderUserId { get; set; }
        public string SenderName { get; set; } = string.Empty;
        public string MessageContent { get; set; } = string.Empty;
        public bool HasAttachments { get; set; } = false;
        public DateTime SentDateTime { get; set; }
        public List<AttachmentDto> Attachments { get; set; } = new();
        public MessageStatus Status { get; set; } = MessageStatus.Sent;
    }

    /// <summary>
    /// Lightweight DTO for attachments in real-time transmission
    /// </summary>
    public class AttachmentDto
    {
        public Guid AttachmentId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long FileSizeBytes { get; set; }
        public string? ThumbnailUrl { get; set; }
        public string? DownloadUrl { get; set; }
    }

    /// <summary>
    /// DTO for sending messages via SignalR
    /// </summary>
    public class SendMessageDto
    {
        public Guid ConversationId { get; set; }
        public string MessageContent { get; set; } = string.Empty;
        public List<MediaFileDto> MediaFiles { get; set; } = new();
    }

    /// <summary>
    /// DTO for media file uploads
    /// </summary>
    public class MediaFileDto
    {
        public string FileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public string Base64Content { get; set; } = string.Empty;
        public long FileSizeBytes { get; set; }
    }

    /// <summary>
    /// DTO for conversation participants
    /// </summary>
    public class ConversationDto
    {
        public string ConversationId { get; set; } = string.Empty;
        public string? Name { get; set; }
        public bool IsGroup { get; set; }
        public DateTime CreatedAt { get; set; }
        public List<ParticipantDto> Participants { get; set; } = new();
        public MessageDto? LastMessage { get; set; }
        public int UnreadCount { get; set; }
    }

    /// <summary>
    /// DTO for conversation participants
    /// </summary>
    public class ParticipantDto
    {
        public Guid UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string? AvatarUrl { get; set; }
        public bool IsOnline { get; set; }
        public DateTime? LastSeen { get; set; }
    }

    /// <summary>
    /// DTO for typing indicators
    /// </summary>
    public class TypingIndicatorDto
    {
        public Guid ConversationId { get; set; }
        public Guid UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public bool IsTyping { get; set; }
    }

    /// <summary>
    /// DTO for message read receipts
    /// </summary>
    public class MessageReceiptDto
    {
        public Guid MessageId { get; set; }
        public Guid UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public DateTime ReadDateTime { get; set; }
    }

    /// <summary>
    /// Message status enumeration
    /// </summary>
    public enum MessageStatus
    {
        Sending = 0,
        Sent = 1,
        Delivered = 2,
        Read = 3,
        Failed = 4
    }

    /// <summary>
    /// DTO for real-time notifications
    /// </summary>
    public class NotificationDto
    {
        public string Type { get; set; } = string.Empty;
        public Guid ConversationId { get; set; }
        public Guid? UserId { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public object? Data { get; set; }
    }
}
