﻿using Azure.Communication;
using Azure.Communication.Chat;
using Azure.Communication.Identity;
using MessageContracts;
using MessageDataAccessLayer.Implementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MessageBusinessLayer.CommandHandler
{
    public class ConversationsCommandHandler : IConversationsCommandHandler<Conversation>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<ConversationsCommandHandler> _logger;
        private readonly CommunicationIdentityClient _identityClient;
        private readonly string _connectionString;
        private readonly Uri _endpoint;

        public ConversationsCommandHandler(
            IConfiguration configuration,
            IUnitOfWork unitOfWork,
            ILogger<ConversationsCommandHandler> logger)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;

            _connectionString = Environment.GetEnvironmentVariable("AzureCommunicationServices__ConnectionString");
            if (string.IsNullOrEmpty(_connectionString))
            {
                _logger.LogError("Azure Communication Services connection string not found.");
                throw new InvalidOperationException("Azure Communication Services connection string is not configured.");
            }

            try
            {
                _identityClient = new CommunicationIdentityClient(_connectionString);

                // Parse endpoint from connection string
                var endpointPart = _connectionString.Split(';')
                    .FirstOrDefault(part => part.StartsWith("endpoint=", StringComparison.OrdinalIgnoreCase));

                if (endpointPart == null)
                    throw new InvalidOperationException("Endpoint not found in connection string");

                _endpoint = new Uri(endpointPart.Substring("endpoint=".Length));

                _logger.LogInformation("ACS Identity client initialized for conversation management");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize ACS Identity client for conversations");
                throw;
            }
        }

        public async Task<Conversation?> CreateConversationAsync(Conversation conversationModel, Guid creatorUserId)
        {
            if (conversationModel == null)
            {
                _logger.LogError("Conversation model is null");
                return null;
            }

            try
            {
                // Fetch creator from DB
                var creator = await _unitOfWork.UserRepository.GetByIdAsync(creatorUserId);
                if (creator == null || string.IsNullOrEmpty(creator.AcsUserId))
                {
                    _logger.LogError("Creator {CreatorId} not found or missing ACS user ID", creatorUserId);
                    return null;
                }

                // Get access token for creator (required to create the chat thread)
                var tokenResponse = await _identityClient.GetTokenAsync(
                    new CommunicationUserIdentifier(creator.AcsUserId),
                    new[] { CommunicationTokenScope.Chat }
                );

                if (tokenResponse == null || string.IsNullOrEmpty(tokenResponse.Value.Token))
                {
                    _logger.LogError("Failed to get ACS token for creator {CreatorId}", creatorUserId);
                    return null;
                }

                var tokenCredential = new CommunicationTokenCredential(tokenResponse.Value.Token);
                var chatClient = new ChatClient(_endpoint, tokenCredential);

                // Create ACS Chat Thread with only the creator
                var createChatThreadResult = await chatClient.CreateChatThreadAsync(
                    topic: conversationModel.IsGroup ? (conversationModel.Name ?? "Group Conversation") : "1:1 Chat",
                    participants: new[]
                    {
                new ChatParticipant(new CommunicationUserIdentifier(creator.AcsUserId))
                {
                    DisplayName = creator.Name
                }
                    }
                );

                if (createChatThreadResult?.Value?.ChatThread == null)
                {
                    _logger.LogError("Failed to create chat thread for creator {CreatorId}", creatorUserId);
                    return null;
                }

                var acsThreadId = createChatThreadResult.Value.ChatThread.Id;

                // Ensure creator is in participants
                var participantIds = conversationModel.Participants
                    .Select(p => p.UserId)
                    .Distinct()
                    .ToList();

                if (!participantIds.Contains(creatorUserId))
                    participantIds.Add(creatorUserId);

                var acsParticipants = new List<ChatParticipant>();
                var conversationParticipants = new List<ConversationParticipant>();

                // Traverse once for both ACS + DB participants
                foreach (var participantId in participantIds)
                {
                    var user = await _unitOfWork.UserRepository.GetByIdAsync(participantId);
                    if (user != null && !string.IsNullOrEmpty(user.AcsUserId))
                    {
                        acsParticipants.Add(new ChatParticipant(new CommunicationUserIdentifier(user.AcsUserId))
                        {
                            DisplayName = user.Name
                        });

                        conversationParticipants.Add(new ConversationParticipant
                        {
                            ConversationId = acsThreadId,
                            UserId = participantId,
                            IsAdmin = participantId == creatorUserId,
                            JoinedAt = DateTime.UtcNow,
                            User = user
                        });
                    }
                    else
                    {
                        _logger.LogWarning("User {UserId} not found or missing ACS user ID", participantId);
                    }
                }

                if (!acsParticipants.Any())
                {
                    _logger.LogError("No valid participants found for conversation by creator {CreatorId}", creatorUserId);
                    return null;
                }

                // Add all participants to ACS thread
                var chatThreadClient = chatClient.GetChatThreadClient(acsThreadId);
                foreach (var participant in acsParticipants)
                {
                    try
                    {
                        await chatThreadClient.AddParticipantAsync(participant);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to add participant {Participant}");
                    }
                }

                // Save conversation in DB
                conversationModel.ConversationId = acsThreadId;
                conversationModel.CreatedByUserId = creatorUserId;
                conversationModel.CreatedAt = DateTime.UtcNow;
                conversationModel.Participants = conversationParticipants;

                await _unitOfWork.ConversationRepository.AddAsync(conversationModel);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation(
                    "Created ACS conversation {ConversationId} with {ParticipantCount} participants",
                    conversationModel.ConversationId, acsParticipants.Count
                );

                return conversationModel;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected failure while creating conversation for creator {CreatorId}", creatorUserId);
                return null;
            }
        }


        public async Task<Conversation?> AddParticipantsToExistingConversationAsync(string conversationId, Conversation conversationModel, Guid addedByUserId)
        {
            if (conversationModel == null)
            {
                _logger.LogError("Conversation model is null");
                return null;
            }

            if (string.IsNullOrWhiteSpace(conversationId))
            {
                _logger.LogError("ConversationId is null or empty");
                return null;
            }

            try
            {
                // Get existing conversation from DB
                var conversation = await _unitOfWork.ConversationRepository.GetByIdAsync(Guid.Parse(conversationId));
                if (conversation == null)
                {
                    _logger.LogError("Conversation {ConversationId} not found", conversationId);
                    return null;
                }

                // Get user who is adding participants
                var addedByUser = await _unitOfWork.UserRepository.GetByIdAsync(addedByUserId);
                if (addedByUser == null || string.IsNullOrEmpty(addedByUser.AcsUserId))
                {
                    _logger.LogError("Adding user {AddedByUserId} not found or missing ACS user ID", addedByUserId);
                    return null;
                }

                // Get token and create ACS chat client
                var tokenResponse = await _identityClient.GetTokenAsync(
                    new CommunicationUserIdentifier(addedByUser.AcsUserId),
                    new[] { CommunicationTokenScope.Chat }
                );

                if (tokenResponse == null || string.IsNullOrEmpty(tokenResponse.Value.Token))
                {
                    _logger.LogError("Failed to get ACS token for user {AddedByUserId}", addedByUserId);
                    return null;
                }

                var tokenCredential = new CommunicationTokenCredential(tokenResponse.Value.Token);
                var chatClient = new ChatClient(_endpoint, tokenCredential);
                var chatThreadClient = chatClient.GetChatThreadClient(conversation.ConversationId);

                // Prepare participant IDs
                var participantIds = conversationModel.Participants
                    .Select(p => p.UserId)
                    .Distinct()
                    .ToList();

                var acsParticipants = new List<ChatParticipant>();
                var dbParticipants = new List<ConversationParticipant>();

                // Traverse once to prepare ACS + DB participants
                foreach (var participantId in participantIds)
                {
                    // Skip if already exists in conversation
                    if (conversation.Participants.Any(p => p.UserId == participantId))
                    {
                        _logger.LogInformation("User {UserId} already in conversation {ConversationId}, skipping", participantId, conversationId);
                        continue;
                    }

                    var user = await _unitOfWork.UserRepository.GetByIdAsync(participantId);
                    if (user != null && !string.IsNullOrEmpty(user.AcsUserId))
                    {
                        acsParticipants.Add(new ChatParticipant(new CommunicationUserIdentifier(user.AcsUserId))
                        {
                            DisplayName = user.Name
                        });

                        dbParticipants.Add(new ConversationParticipant
                        {
                            ConversationParticipantId = Guid.NewGuid(),
                            ConversationId = conversation.ConversationId,
                            UserId = participantId,
                            IsAdmin = false,
                            JoinedAt = DateTime.UtcNow,
                            User = user
                        });
                    }
                    else
                    {
                        _logger.LogWarning("User {UserId} not found or missing ACS user ID, skipping", participantId);
                    }
                }

                if (!acsParticipants.Any())
                {
                    _logger.LogWarning("No valid participants to add for conversation {ConversationId}", conversationId);
                    return conversation;
                }

                // Add participants to existing ACS thread
                try
                {
                    await chatThreadClient.AddParticipantsAsync(acsParticipants);
                    _logger.LogInformation("Added {Count} participants to ACS thread {ConversationId}", acsParticipants.Count, conversationId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to add participants to ACS thread {ConversationId}", conversationId);
                }

                // Add new participants to DB
                foreach (var participant in dbParticipants)
                {
                    conversation.Participants.Add(participant);
                }

                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Added {Count} participants to DB for conversation {ConversationId}", dbParticipants.Count, conversationId);

                return conversation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected failure while adding participants to conversation {ConversationId}", conversationId);
                return null;
            }
        }


        public async Task RemoveParticipantsAsync(Guid conversationId, IEnumerable<Guid> userIds, Guid removedByUserId)
        {
            if (userIds == null || !userIds.Any())
            {
                _logger.LogWarning("No userIds provided to remove from conversation {ConversationId}", conversationId);
                return;
            }

            try
            {
                var conversation = await _unitOfWork.ConversationRepository.GetByIdAsync(conversationId);
                if (conversation == null)
                {
                    _logger.LogError("Conversation {ConversationId} not found", conversationId);
                    return;
                }

                var removedByUser = await _unitOfWork.UserRepository.GetByIdAsync(removedByUserId);
                if (removedByUser == null || string.IsNullOrEmpty(removedByUser.AcsUserId))
                {
                    _logger.LogError("Removing user {RemovedByUserId} not found or missing ACS user ID", removedByUserId);
                    return;
                }

                var tokenResponse = await _identityClient.GetTokenAsync(
                    new CommunicationUserIdentifier(removedByUser.AcsUserId),
                    new[] { CommunicationTokenScope.Chat }
                );

                if (tokenResponse == null || string.IsNullOrEmpty(tokenResponse.Value.Token))
                {
                    _logger.LogError("Failed to get ACS token for user {RemovedByUserId}", removedByUserId);
                    return;
                }

                var tokenCredential = new CommunicationTokenCredential(tokenResponse.Value.Token);
                var chatClient = new ChatClient(_endpoint, tokenCredential);
                var chatThreadClient = chatClient.GetChatThreadClient(conversation.ConversationId);

                foreach (var userId in userIds.Distinct())
                {
                    var user = await _unitOfWork.UserRepository.GetByIdAsync(userId);
                    if (user == null || string.IsNullOrEmpty(user.AcsUserId))
                    {
                        _logger.LogWarning("User {UserId} not found or missing ACS user ID, skipping removal", userId);
                        continue;
                    }

                    // Remove from ACS
                    try
                    {
                        await chatThreadClient.RemoveParticipantAsync(new CommunicationUserIdentifier(user.AcsUserId));
                        _logger.LogInformation("Removed participant {UserId} from ACS thread {ConversationId}", userId, conversationId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to remove participant {UserId} from ACS thread {ConversationId}", userId, conversationId);
                    }

                    // Remove from DB
                    var participant = conversation.Participants.FirstOrDefault(p => p.UserId == userId);
                    if (participant != null)
                    {
                        conversation.Participants.Remove(participant);
                    }
                }

                await _unitOfWork.SaveAsync();
                _logger.LogInformation("Removed participants from DB for conversation {ConversationId}", conversationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected failure while removing participants from conversation {ConversationId}", conversationId);
            }
        }


        public async Task UpdateConversationNameAsync(Guid conversationId, string newName, Guid updatedByUserId)
        {
            if (string.IsNullOrWhiteSpace(newName))
            {
                _logger.LogWarning("New conversation name is null or empty for conversation {ConversationId}", conversationId);
                return;
            }

            try
            {
                var conversation = await _unitOfWork.ConversationRepository.GetByIdAsync(conversationId);
                if (conversation == null)
                {
                    _logger.LogError("Conversation {ConversationId} not found", conversationId);
                    return;
                }

                if (!conversation.IsGroup)
                {
                    _logger.LogWarning("Cannot update name for non-group conversation {ConversationId}", conversationId);
                    return;
                }

                var updatedByUser = await _unitOfWork.UserRepository.GetByIdAsync(updatedByUserId);
                if (updatedByUser == null || string.IsNullOrEmpty(updatedByUser.AcsUserId))
                {
                    _logger.LogError("Updating user {UpdatedByUserId} not found or missing ACS user ID", updatedByUserId);
                    return;
                }

                var tokenResponse = await _identityClient.GetTokenAsync(
                    new CommunicationUserIdentifier(updatedByUser.AcsUserId),
                    new[] { CommunicationTokenScope.Chat }
                );

                if (tokenResponse == null || string.IsNullOrEmpty(tokenResponse.Value.Token))
                {
                    _logger.LogError("Failed to get ACS token for user {UpdatedByUserId}", updatedByUserId);
                    return;
                }

                var tokenCredential = new CommunicationTokenCredential(tokenResponse.Value.Token);
                var chatClient = new ChatClient(_endpoint, tokenCredential);
                var chatThreadClient = chatClient.GetChatThreadClient(conversation.ConversationId);

                try
                {
                    await chatThreadClient.UpdateTopicAsync(newName);
                    _logger.LogInformation("Updated ACS thread topic for conversation {ConversationId} to '{NewName}'", conversationId, newName);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to update ACS thread topic for conversation {ConversationId}", conversationId);
                }

                // Update in DB
                conversation.Name = newName;
                await _unitOfWork.SaveAsync();
                _logger.LogInformation("Updated conversation {ConversationId} name in DB to '{NewName}'", conversationId, newName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected failure while updating conversation {ConversationId} name", conversationId);
            }
        }

    }
}