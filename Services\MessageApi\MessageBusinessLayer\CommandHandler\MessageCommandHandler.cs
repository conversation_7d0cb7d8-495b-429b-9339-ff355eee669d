using MessageContracts;
using MessageDataAccessLayer.Implementation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MessageBusinessLayer.CommandHandler
{
    public class MessageCommandHandler : IMessageCommandHandler<Message>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<MessageCommandHandler> _logger;
        private readonly IAzureBlobStorageService<MessageAttachment> _blobStorageService;

        public MessageCommandHandler(
            IUnitOfWork unitOfWork,
            ILogger<MessageCommandHandler> logger,
            IAzureBlobStorageService<MessageAttachment> blobStorageService)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _blobStorageService = blobStorageService;
        }

        /// <summary>
        /// Send a new message
        /// </summary>
        public async Task<Message> SendMessageAsync(Message message)
        {
            try
            {
                

                await _unitOfWork.MessageRepository.AddAsync(message);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Message {MessageId} sent in conversation {ConversationId}", message.MessageId, message.ConversationId);
                return message;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send message in conversation {ConversationId}", message.ConversationId);
                throw;
            }
        }

        /// <summary>
        /// Update message content
        /// </summary>
        public async Task<Message> UpdateMessageAsync(Guid messageId, string newContent)
        {
            try
            {
                var message = await _unitOfWork.MessageRepository.GetByIdAsync(messageId);
                if (message == null)
                {
                    _logger.LogWarning("Message {MessageId} not found for update", messageId);
                    return null;
                }

                message.MessageContent = newContent;
                message.SentDateTime = DateTime.UtcNow; // Optionally update timestamp
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Updated message {MessageId}", messageId);
                return message;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update message {MessageId}", messageId);
                throw;
            }
        }

        /// <summary>
        /// Soft delete multiple messages
        /// </summary>
        public async Task DeleteMessagesAsync(IEnumerable<Guid> messageIds)
        {
            try
            {
                var messages = await _unitOfWork.MessageRepository
                    .Query()
                    .Where(m => messageIds.Contains(m.MessageId))
                    .ToListAsync();

                foreach (var message in messages)
                {
                    message.IsDeleted = true;
                    message.DeletedDateTime = DateTime.UtcNow;
                }

                await _unitOfWork.SaveAsync();
                _logger.LogInformation("Soft-deleted {Count} messages", messages.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to soft delete messages");
                throw;
            }
        }

        /// <summary>
        /// Add or update attachments
        /// </summary>
        public async Task<IEnumerable<MessageAttachment>> UpdateMessageAttachmentsAsync(
            Guid messageId,
            IEnumerable<(Guid? AttachmentId, byte[] Content, string FileName, string ContentType)> files)
        {
            var updatedAttachments = new List<MessageAttachment>();

            try
            {
                var message = await _unitOfWork.MessageRepository
                    .Query()
                    .Include(m => m.Attachments)
                    .FirstOrDefaultAsync(m => m.MessageId == messageId);

                if (message == null)
                {
                    _logger.LogWarning("Message {MessageId} not found for updating attachments", messageId);
                    return updatedAttachments;
                }

                foreach (var file in files)
                {
                    if (file.AttachmentId.HasValue)
                    {
                        var existingAttachment = message.Attachments.FirstOrDefault(a => a.AttachmentId == file.AttachmentId.Value);
                        if (existingAttachment != null)
                        {
                            try { await _blobStorageService.DeleteAttachmentAsync(existingAttachment.BlobReference); } catch { }

                            var blobRef = await _blobStorageService.UploadAttachmentAsync(file.Content, file.FileName, file.ContentType);
                            existingAttachment.BlobReference = blobRef;
                            existingAttachment.FileName = file.FileName;
                            existingAttachment.ContentType = file.ContentType;
                            existingAttachment.FileSizeBytes = file.Content.Length;
                            existingAttachment.UploadedDateTime = DateTime.UtcNow;

                            updatedAttachments.Add(existingAttachment);
                        }
                    }
                    else
                    {
                        var blobRef = await _blobStorageService.UploadAttachmentAsync(file.Content, file.FileName, file.ContentType);

                        var attachment = new MessageAttachment
                        {
                            AttachmentId = Guid.NewGuid(),
                            MessageId = messageId,
                            BlobReference = blobRef,
                            FileName = file.FileName,
                            ContentType = file.ContentType,
                            FileSizeBytes = file.Content.Length,
                            UploadedDateTime = DateTime.UtcNow
                        };

                        message.Attachments.Add(attachment);
                        updatedAttachments.Add(attachment);
                    }
                }

                message.HasAttachments = message.Attachments.Any();
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Updated {Count} attachments for message {MessageId}", updatedAttachments.Count, messageId);
                return updatedAttachments;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update attachments for message {MessageId}", messageId);
                throw;
            }
        }

        /// <summary>
        /// Mark message as read
        /// </summary>
        public async Task MarkMessageAsReadAsync(Guid messageId, Guid userId)
        {
            try
            {
                var message = await _unitOfWork.MessageRepository
                    .Query()
                    .Include(m => m.Receipts)
                    .FirstOrDefaultAsync(m => m.MessageId == messageId);

                if (message == null)
                {
                    _logger.LogWarning("Message {MessageId} not found for marking read", messageId);
                    return;
                }

                var receipt = message.Receipts.FirstOrDefault(r => r.UserId == userId);
                if (receipt == null)
                {
                    receipt = new MessageReceipt
                    {
                        ReceiptId = Guid.NewGuid(),
                        MessageId = messageId,
                        UserId = userId,
                        ReadAt = DateTime.UtcNow
                    };
                    message.Receipts.Add(receipt);
                }
                else
                {
                    receipt.ReadAt = DateTime.UtcNow;
                }

                await _unitOfWork.SaveAsync();
                _logger.LogInformation("Marked message {MessageId} as read by user {UserId}", messageId, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to mark message {MessageId} as read", messageId);
                throw;
            }
        }
    }
}
