﻿using MessageContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MessageBusinessLayer
{
    public interface IConversationsCommandHandler<T>
    {
        Task<Conversation?> CreateConversationAsync(Conversation conversationModel, Guid creatorUserId);
        Task<Conversation?> AddParticipantsToExistingConversationAsync(string conversationId, Conversation conversationModel, Guid addedByUserId);
        Task RemoveParticipantsAsync(Guid conversationId, IEnumerable<Guid> userIds, Guid removedByUserId);
        Task UpdateConversationNameAsync(Guid conversationId, string newName, Guid updatedByUserId);

    }
}
