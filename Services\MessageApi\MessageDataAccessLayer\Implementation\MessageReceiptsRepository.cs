﻿using DataAccessLayer.Context;
using MessageContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MessageDataAccessLayer.Implementation
{
    public class MessageReceiptsRepository : GenericRepository<MessageReceipt>, IMessageReceiptsRepository
    {
        private readonly MessageApiDatabaseContext _context;
        public MessageReceiptsRepository(MessageApiDatabaseContext context) : base(context)
        {
            _context = context;
        }
    }
}
