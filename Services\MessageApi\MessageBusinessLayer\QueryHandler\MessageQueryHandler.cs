using MessageContracts;
using MessageDataAccessLayer.Implementation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace MessageBusinessLayer.QueryHandler
{
    public class MessageQueryHandler : IMessageQueryHandler<Message>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<MessageQueryHandler> _logger;
        private readonly IAzureBlobStorageService<MessageAttachment> _blobStorageService;

        public MessageQueryHandler(
            IUnitOfWork unitOfWork,
            ILogger<MessageQueryHandler> logger,
            IAzureBlobStorageService<MessageAttachment> blobStorageService)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _blobStorageService = blobStorageService;
        }
    

        /// <summary>
        /// Get paginated messages for a conversation.
        /// </summary>
        public async Task<IEnumerable<Message>> GetMessagesByConversationAsync(Guid conversationId, int page = 1, int pageSize = 50)
        {
            _logger.LogInformation("Fetching messages for conversation {ConversationId}", conversationId);

            var messages = await _unitOfWork.MessageRepository
                .Query()
                .Include(m => m.Attachments)
                .Include(m => m.Receipts)
                .Include(m => m.Sender)
                .Where(m => m.ConversationId == conversationId && !m.IsDeleted)
                .OrderByDescending(m => m.SentDateTime)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // Add pre-signed download URLs for attachments
            foreach (var msg in messages.Where(m => m.HasAttachments))
            {
                foreach (var att in msg.Attachments)
                {
                    att.ScanResult ??= "Pending"; // fallback
                    att.ContentType ??= "application/octet-stream";

                    // Generate a temporary SAS URL for frontend
                    att.TempDownloadUrl = _blobStorageService.GetAttachmentUrl(att.BlobReference);
                }
            }

            return messages;
        }

        /// <summary>
        /// Get a single message by ID.
        /// </summary>
        public async Task<Message?> GetMessageByIdAsync(Guid messageId)
        {
            var message = await _unitOfWork.MessageRepository
                .Query()
                .Include(m => m.Attachments)
                .Include(m => m.Receipts)
                .Include(m => m.Sender)
                .FirstOrDefaultAsync(m => m.MessageId == messageId && !m.IsDeleted);

            if (message == null) return null;

            if (message.HasAttachments)
            {
                foreach (var att in message.Attachments)
                {
                    att.TempDownloadUrl = _blobStorageService.GetAttachmentUrl(att.BlobReference);
                }
            }

            return message;
        }

        /// <summary>
        /// Get a single attachment metadata.
        /// </summary>
        public async Task<MessageAttachment?> GetMessageAttachmentAsync(Guid attachmentId)
        {
            var attachment = await _unitOfWork.AttachmentRepository.GetByIdAsync(attachmentId);

            if (attachment != null)
            {
                attachment.TempDownloadUrl = _blobStorageService.GetAttachmentUrl(attachment.BlobReference);
            }

            return attachment;
        }

        /// <summary>
        /// Get pre-signed URL for frontend to directly download.
        /// </summary>
        public async Task<string> GetAttachmentDownloadUrlAsync(Guid attachmentId)
        {
            var attachment = await _unitOfWork.AttachmentRepository.GetByIdAsync(attachmentId);
            if (attachment == null)
                throw new FileNotFoundException($"Attachment {attachmentId} not found");

            return _blobStorageService.GetAttachmentUrl(attachment.BlobReference);
        }

        /// <summary>
        /// Backend service-to-service download (not recommended for frontend).
        /// </summary>
        public async Task<byte[]> DownloadAttachmentAsync(Guid attachmentId)
        {
            var attachment = await _unitOfWork.AttachmentRepository.GetByIdAsync(attachmentId);
            if (attachment == null)
                throw new FileNotFoundException($"Attachment {attachmentId} not found");

            return await _blobStorageService.DownloadAttachmentAsync(attachment.BlobReference);
        }
    }
}
