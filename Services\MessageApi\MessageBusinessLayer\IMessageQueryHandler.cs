using MessageContracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MessageBusinessLayer
{
    public interface IMessageQueryHandler<T>
    {
        Task<IEnumerable<Message>> GetMessagesByConversationAsync(Guid conversationId, int page = 1, int pageSize = 50);
        Task<Message?> GetMessageByIdAsync(Guid messageId);
        Task<MessageAttachment?> GetMessageAttachmentAsync(Guid attachmentId);
        Task<string> GetAttachmentDownloadUrlAsync(Guid attachmentId);
        Task<byte[]> DownloadAttachmentAsync(Guid attachmentId);
    }
}
