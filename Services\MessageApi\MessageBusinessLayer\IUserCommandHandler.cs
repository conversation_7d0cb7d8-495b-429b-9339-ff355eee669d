﻿using MessageContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MessageBusinessLayer
{
    public interface IUserCommandHandler<T>
    {
        Task CreateAcsUserAsync(User user);
        Task<(string Token, DateTimeOffset ExpiresOn)> GetUserTokenAsync(string acsUserId);
        Task DeleteAcsUserAsync(User user);
    }
}
