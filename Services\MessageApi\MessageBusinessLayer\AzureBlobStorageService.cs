using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using MessageContracts;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Threading.Tasks;

namespace MessageBusinessLayer
{
    

    public class AzureBlobStorageService : IAzureBlobStorageService<MessageAttachment>
    {
        private readonly string _connectionString;
        private readonly string _containerName;
        private readonly ILogger<AzureBlobStorageService> _logger;
        private readonly BlobServiceClient _blobServiceClient;
        private readonly BlobContainerClient _containerClient;

        public AzureBlobStorageService(IConfiguration configuration, ILogger<AzureBlobStorageService> logger)
        {
            _logger = logger;
            
           
            _connectionString = Environment.GetEnvironmentVariable("AZURE_BLOB_CONNECTION_STRING") 
                ?? configuration.GetConnectionString("AzureBlobStorage")
                ?? throw new InvalidOperationException("Azure Blob Storage connection string not found");
            
            _containerName = Environment.GetEnvironmentVariable("AZURE_BLOB_CONTAINER_NAME") 
                ?? configuration["AzureBlobStorage:ContainerName"] 
                ?? "message-attachments";

            _blobServiceClient = new BlobServiceClient(_connectionString);
            _containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
            
            // Ensure container exists
            _ = Task.Run(async () =>
            {
                try
                {
                    await _containerClient.CreateIfNotExistsAsync(PublicAccessType.None);
                    _logger.LogInformation($"Azure Blob container  is ready");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Failed to create blob container ");
                }
            });
        }

        public async Task<string> UploadAttachmentAsync(byte[] content, string fileName, string contentType)
        {
            try
            {
                // Generate unique blob name
                var blobFileName = $"{Guid.NewGuid()}_{fileName}";
                var blobClient = _containerClient.GetBlobClient(blobFileName);

                _logger.LogInformation($"Uploading attachment to blob");

                // Set blob headers
                var blobHttpHeaders = new BlobHttpHeaders
                {
                    ContentType = contentType
                };

                // Upload with metadata
                var metadata = new Dictionary<string, string>
                {
                    ["OriginalFileName"] = fileName,
                    ["UploadedDateTime"] = DateTime.UtcNow.ToString("O"),
                    ["FileSizeBytes"] = content.Length.ToString()
                };

                using var stream = new MemoryStream(content);
                await blobClient.UploadAsync(stream, new BlobUploadOptions
                {
                    HttpHeaders = blobHttpHeaders,
                    Metadata = metadata
                });

                _logger.LogInformation($"Successfully uploaded attachment to blob");
                return blobFileName;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to upload attachment to blob storage");
                throw;
            }
        }

        public async Task<byte[]> DownloadAttachmentAsync(string blobFileName)
        {
            try
            {
                var blobClient = _containerClient.GetBlobClient(blobFileName);
                
                _logger.LogInformation($"Downloading attachment from blob");

                if (!await blobClient.ExistsAsync())
                {
                    throw new FileNotFoundException($"Blob not found: {blobFileName}");
                }

                var response = await blobClient.DownloadContentAsync();
                var content = response.Value.Content.ToArray();

                _logger.LogInformation($"Successfully downloaded attachment from blob");
                return content;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to download attachment from blob storage");
                throw;
            }
        }

        public async Task<bool> DeleteAttachmentAsync(string blobFileName)
        {
            try
            {
                var blobClient = _containerClient.GetBlobClient(blobFileName);
                
                _logger.LogInformation($"Deleting attachment from blob");

                var response = await blobClient.DeleteIfExistsAsync();
                
                if (response.Value)
                {
                    _logger.LogInformation($"Successfully deleted attachment from blob");
                }
                else
                {
                    _logger.LogWarning($"Blob not found for deletion");
                }

                return response.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to delete attachment from blob storage");
                return false;
            }
        }

        public async Task<bool> AttachmentExistsAsync(string blobFileName)
        {
            try
            {
                var blobClient = _containerClient.GetBlobClient(blobFileName);
                var response = await blobClient.ExistsAsync();
                return response.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to check if attachment exists in blob storage");
                return false;
            }
        }

        public string GetAttachmentUrl(string blobFileName)
        {
            var blobClient = _containerClient.GetBlobClient(blobFileName);
            return blobClient.Uri.ToString();
        }
    }
}
