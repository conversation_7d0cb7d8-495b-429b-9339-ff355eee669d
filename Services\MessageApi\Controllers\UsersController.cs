﻿using MessageBusinessLayer;
using MessageContracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

namespace MessageApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UsersController : ControllerBase
    {
        private readonly ILogger<UsersController> _logger;
        private readonly IStringLocalizer<UsersController> _localizer;
        private readonly IUserQueryHandler<User> _userQueryHandler;
        private readonly IUserCommandHandler<User> _userCommandHandler;

        public UsersController(ILogger<UsersController> logger, IStringLocalizer<UsersController> localizer, IUserQueryHandler<User> userQueryHandler, IUserCommandHandler<User> userCommandHandler)
        {
            _logger = logger;
            _localizer = localizer;
            _userQueryHandler = userQueryHandler;
            _userCommandHandler = userCommandHandler;
        }
       
        [HttpPost("/acs/register-user/")]
        public async Task<IActionResult> CreateAcsUser([FromBody] User user)
        {
            if (user == null)
            {
                _logger.LogWarning(_localizer["InvalidData"]);
                return BadRequest(_localizer["UsersInvalidData"]);
            }

            try
            {
                _logger.LogInformation(_localizer["AddingNewUser"]);
                await _userCommandHandler.CreateAcsUserAsync(user);
                _logger.LogInformation(_localizer["UserAddedSuccessfully"]);

                return Ok(new { Message = _localizer["UserAddedSuccessfully"], User = user });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorWhileAddingUser"]);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new { Message = _localizer["ErrorWhileAddingUser"], Details = ex.Message });
            }
        }

        [HttpPost("api/users/{acsId}/acs/token")]
        public async Task<IActionResult> GenerateAcsToken([FromRoute] string acsId)
        {
            if (string.IsNullOrWhiteSpace(acsId))
            {
                _logger.LogWarning(_localizer["InvalidUserId"]);
                return BadRequest(new { Message = _localizer["InvalidUserId"] });
            }

            try
            {
                // Step 1: Get token + expiry from CommandHandler
                var (token, expiresOn) = await _userCommandHandler.GetUserTokenAsync(acsId);

                // Step 2: Calculate remaining seconds until expiry (optional)
                var remainingSeconds = (int)(expiresOn - DateTimeOffset.UtcNow).TotalSeconds;

                _logger.LogInformation("ACS token generated successfully for user {AcsId}", acsId);

                // Step 3: Return structured response
                return Ok(new
                {
                    Token = token,
                    ExpiresOn = expiresOn,
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating ACS token for user {UserId}");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new { Message = _localizer["ErrorWhileGeneratingToken"], Details = ex.Message });
            }
        }
        [HttpGet("/GetAcsUser/{userId:guid}/{orgId:guid}")]
        public async Task<ActionResult<User>> GetAcsUserByIds(Guid userId, Guid orgId)
        {
            try
            {
                var user = await _userQueryHandler.GetAcsUserById(userId, orgId);

                if (user == null)
                {
                    _logger.LogWarning(_localizer["UsersNotFound"]);
                    return NotFound(_localizer["UsersNotFound"]);
                }

                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorWhileFetchingUsers"]);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new { Message = _localizer["ErrorWhileFetchingUsers"], Details = ex.Message });
            }
        }

        [HttpDelete("{id:guid}/{OrgID:Guid}")]
        public async Task<IActionResult> DeleteFacility([FromBody] User user)
        {
            if (user == null || user.UserId == Guid.Empty || user.OrgId == Guid.Empty)
            {
                _logger.LogWarning("Invalid user data provided for delete.");
                return BadRequest(new { Message = _localizer["InvalidData"] });
            }

            try
            {
                await _userCommandHandler.DeleteAcsUserAsync(user);

                _logger.LogInformation("User {UserId} deleted successfully for org {OrgId}");
                return Ok(new { Message = _localizer["UserDeletedSuccessfully"]});
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user {UserId} in org {OrgId}");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new { Message = _localizer["ErrorWhileDeletingUser"], Details = ex.Message });
            }
        }



    }
}
