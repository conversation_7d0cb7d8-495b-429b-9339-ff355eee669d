﻿using System;

namespace MessageContracts
{
    public class MessageReceipt:IContract
    {
        public Guid ReceiptId { get; set; }
        public Guid MessageId { get; set; }
        public Guid UserId { get; set; }

        public DateTime? DeliveredAt { get; set; }
        public DateTime? ReadAt { get; set; }

        // Navigation
        public virtual Message Message { get; set; }
        public virtual User User { get; set; }
    }
}
