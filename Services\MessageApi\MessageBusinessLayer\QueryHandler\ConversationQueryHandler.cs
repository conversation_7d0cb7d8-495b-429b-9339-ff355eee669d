﻿using MessageContracts;
using MessageDataAccessLayer.Implementation;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;


namespace MessageBusinessLayer.QueryHandler
{
    public class ConversationQueryHandler : IConversationQueryHandler<Conversation>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<MessageQueryHandler> _logger;
        public ConversationQueryHandler(
            IUnitOfWork unitOfWork,
            ILogger<MessageQueryHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }
        /// <summary>
        /// Get paginated conversations where the user is a participant
        /// </summary>
        public async Task<IEnumerable<Conversation>> GetUserConversationsAsync(
            Guid userId, int page = 1, int pageSize = 20)
        {
            _logger.LogInformation(
                "Fetching conversations for user {UserId}, page {Page}, pageSize {PageSize}",
                userId, page, pageSize);

            var conversations = await _unitOfWork.ConversationRepository
                .Query()
                .Include(c => c.Participants.Select(p => p.User))
                .Include(c => c.Messages.Select(m => m.Sender))
                .Where(c => c.Participants.Any(p => p.UserId == userId))
                .OrderByDescending(c => c.Messages
                    .OrderByDescending(m => m.SentDateTime)
                    .Select(m => m.SentDateTime)
                    .FirstOrDefault())
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return conversations;
        }
    }

}
