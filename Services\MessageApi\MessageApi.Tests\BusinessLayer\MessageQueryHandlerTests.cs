using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using System;
using MessageContracts;
using MessageBusinessLayer.QueryHandler;
using MessageDataAccessLayer.Implementation;
using System.Linq;

namespace MessageApi.Tests.BusinessLayer
{
    [TestFixture]
    public class MessageQueryHandlerTests
    {
        private Mock<IConfiguration> _configurationMock;
        private Mock<IUnitOfWork> _unitOfWorkMock;
        private Mock<ILogger<MessageQueryHandler>> _loggerMock;
        private Mock<IStringLocalizer<MessageQueryHandler>> _localizerMock;
        private Mock<IMessageRepository> _messageRepositoryMock;
        private MessageQueryHandler _queryHandler;
        private List<Message> _testMessages;
        private List<MessageAttachment> _testAttachments;

        [SetUp]
        public void Setup()
        {
            _configurationMock = new Mock<IConfiguration>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<MessageQueryHandler>>();
            _localizerMock = new Mock<IStringLocalizer<MessageQueryHandler>>();
            _messageRepositoryMock = new Mock<IMessageRepository>();

            // Setup unit of work to return message repository
            _unitOfWorkMock.Setup(x => x.MessageRepository).Returns(_messageRepositoryMock.Object);

            _testAttachments = new List<MessageAttachment>
            {
                new MessageAttachment
                {
                    AttachmentId = Guid.NewGuid(),
                    FileName = "test.pdf",
                    OriginalFileName = "test.pdf",
                    ContentType = "application/pdf",
                    FileSizeBytes = 1024,
                    BlobFileName = "blob_test.pdf",
                    BlobStorageUrl = "https://test.blob.core.windows.net/attachments/blob_test.pdf",
                    BlobContainerName = "attachments",
                    FileHash = "testhash123",
                    UploadedDateTime = DateTime.UtcNow,
                    IsScanned = true,
                    IsSafe = true
                }
            };

            _testMessages = new List<Message>
            {
                new Message
                {
                    MessageId = Guid.NewGuid(),
                    SenderName = "John Doe",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = "Jane Doe",
                    ReceiverEmailId = "<EMAIL>",
                    Subject = "Test Subject 1",
                    MessageContent = "Test Content 1",
                    SentDateTime = DateTime.UtcNow.AddHours(-2),
                    Status = 1,
                    IsDeleted = false,
                    IsArchived = false,
                    HasAttachments = false,
                    AttachmentCount = 0,
                    Attachments = new List<MessageAttachment>()
                },
                new Message
                {
                    MessageId = Guid.NewGuid(),
                    SenderName = "Jane Doe",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = "John Doe",
                    ReceiverEmailId = "<EMAIL>",
                    Subject = "Test Subject 2",
                    MessageContent = "Test Content 2",
                    SentDateTime = DateTime.UtcNow.AddHours(-1),
                    Status = 1,
                    IsDeleted = false,
                    IsArchived = false,
                    HasAttachments = true,
                    AttachmentCount = 1,
                    Attachments = _testAttachments
                },
                new Message
                {
                    MessageId = Guid.NewGuid(),
                    SenderName = "Bob Smith",
                    SenderEmailId = "<EMAIL>",
                    ReceiverName = "John Doe",
                    ReceiverEmailId = "<EMAIL>",
                    Subject = "Test Subject 3",
                    MessageContent = "Test Content 3",
                    SentDateTime = DateTime.UtcNow.AddMinutes(-30),
                    Status = 1,
                    IsDeleted = false,
                    IsArchived = false,
                    HasAttachments = false,
                    AttachmentCount = 0,
                    Attachments = new List<MessageAttachment>()
                }
            };

            // Update attachment MessageId to match second message
            _testAttachments[0].MessageId = _testMessages[1].MessageId;

            _queryHandler = new MessageQueryHandler(
                _configurationMock.Object,
                _unitOfWorkMock.Object,
                _loggerMock.Object
            );
        }

        #region GetMessagesByEmail Tests

        [Test]
        public async Task GetMessagesByEmail_ValidEmail_ReturnsMessagesWithAttachments()
        {
            // Arrange
            var email = "<EMAIL>";
            var expectedMessages = _testMessages.Where(m =>
                m.SenderEmailId == email || m.ReceiverEmailId == email).ToList();

            _messageRepositoryMock.Setup(x => x.GetMessagesWithAttachmentsByEmailAsync(email))
                .ReturnsAsync(expectedMessages);

            // Act
            var result = await _queryHandler.GetMessagesByEmail(email);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(3);

            // Verify attachment flags are updated correctly
            var messageWithAttachment = result.FirstOrDefault(m => m.MessageId == _testMessages[1].MessageId);
            messageWithAttachment.Should().NotBeNull();
            messageWithAttachment.HasAttachments.Should().BeTrue();
            messageWithAttachment.AttachmentCount.Should().Be(1);

            var messageWithoutAttachment = result.FirstOrDefault(m => m.MessageId == _testMessages[0].MessageId);
            messageWithoutAttachment.Should().NotBeNull();
            messageWithoutAttachment.HasAttachments.Should().BeFalse();
            messageWithoutAttachment.AttachmentCount.Should().Be(0);
        }

        [Test]
        public async Task GetMessagesByEmail_ExceptionThrown_LogsErrorAndThrowsException()
        {
            // Arrange
            var email = "<EMAIL>";
            var testException = new Exception("Database error");
            _messageRepositoryMock.Setup(x => x.GetMessagesWithAttachmentsByEmailAsync(email))
                .ThrowsAsync(testException);

            // Act & Assert
            await FluentActions.Invoking(() => _queryHandler.GetMessagesByEmail(email))
                .Should().ThrowAsync<Exception>()
                .WithMessage("Database error");

            // Verify logger was called for error (following TeyaHealth patterns)
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains($"Error getting messages for email: {email}")),
                    testException,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        #endregion

        #region GetConversation Tests

        [Test]
        public async Task GetConversation_ValidEmails_ReturnsConversationMessages()
        {
            // Arrange
            var senderEmail = "<EMAIL>";
            var receiverEmail = "<EMAIL>";

            _messageRepositoryMock.Setup(x => x.GetAsync())
                .ReturnsAsync(_testMessages);

            // Act
            var result = await _queryHandler.GetConversation(senderEmail, receiverEmail);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2); // Only messages between john and jane

            var resultList = result.ToList();
            resultList.Should().OnlyContain(m =>
                (m.SenderEmailId.Equals(senderEmail, StringComparison.OrdinalIgnoreCase) &&
                 m.ReceiverEmailId.Equals(receiverEmail, StringComparison.OrdinalIgnoreCase)) ||
                (m.SenderEmailId.Equals(receiverEmail, StringComparison.OrdinalIgnoreCase) &&
                 m.ReceiverEmailId.Equals(senderEmail, StringComparison.OrdinalIgnoreCase)));

            // Should be ordered by SentDateTime
            resultList.Should().BeInAscendingOrder(m => m.SentDateTime);
        }

        [Test]
        public async Task GetConversation_ExceptionThrown_ThrowsException()
        {
            // Arrange
            var senderEmail = "<EMAIL>";
            var receiverEmail = "<EMAIL>";
            _messageRepositoryMock.Setup(x => x.GetAsync())
                .ThrowsAsync(new Exception("Database error"));

            // Act & Assert
            await FluentActions.Invoking(() => _queryHandler.GetConversation(senderEmail, receiverEmail))
                .Should().ThrowAsync<Exception>()
                .WithMessage("Database error");
        }

        #endregion

        #region GetMessageById Tests

        [Test]
        public async Task GetMessageById_ExistingId_ReturnsMessage()
        {
            // Arrange
            var messageId = _testMessages[0].MessageId;
            _messageRepositoryMock.Setup(x => x.GetByIdAsync(messageId))
                .ReturnsAsync(_testMessages[0]);

            // Act
            var result = await _queryHandler.GetMessageById(messageId);

            // Assert
            result.Should().NotBeNull();
            result.MessageId.Should().Be(messageId);
            result.SenderName.Should().Be("John Doe");
            result.Subject.Should().Be("Test Subject 1");
        }

        [Test]
        public async Task GetMessageById_NonExistingId_ReturnsNull()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            _messageRepositoryMock.Setup(x => x.GetByIdAsync(messageId))
                .ReturnsAsync((Message)null);

            // Act
            var result = await _queryHandler.GetMessageById(messageId);

            // Assert
            result.Should().BeNull();
        }

        [Test]
        public async Task GetMessageById_ExceptionThrown_ThrowsException()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            _messageRepositoryMock.Setup(x => x.GetByIdAsync(messageId))
                .ThrowsAsync(new Exception("Database error"));

            // Act & Assert
            await FluentActions.Invoking(() => _queryHandler.GetMessageById(messageId))
                .Should().ThrowAsync<Exception>()
                .WithMessage("Database error");
        }

        #endregion

        #region GetUnreadMessages Tests

        [Test]
        public async Task GetUnreadMessages_ValidEmail_ReturnsUnreadMessages()
        {
            // Arrange
            var email = "<EMAIL>";
            var unreadMessages = _testMessages.Where(m => m.ReceiverEmailId == email && m.Status != 3).ToList();

            _messageRepositoryMock.Setup(x => x.GetAsync())
                .ReturnsAsync(_testMessages);

            // Act
            var result = await _queryHandler.GetUnreadMessages(email);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2); // <NAME_EMAIL> with status != 3
            result.Should().OnlyContain(m => m.ReceiverEmailId.Equals(email, StringComparison.OrdinalIgnoreCase));
            result.Should().OnlyContain(m => m.Status != 3);

            // Should be ordered by SentDateTime descending
            var resultList = result.ToList();
            resultList.Should().BeInDescendingOrder(m => m.SentDateTime);
        }

        #endregion

        #region GetSentMessages Tests

        [Test]
        public async Task GetSentMessages_ValidEmail_ReturnsSentMessages()
        {
            // Arrange
            var email = "<EMAIL>";

            _messageRepositoryMock.Setup(x => x.GetAsync())
                .ReturnsAsync(_testMessages);

            // Act
            var result = await _queryHandler.GetSentMessages(email);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1); // Only one message <NAME_EMAIL>
            result.Should().OnlyContain(m => m.SenderEmailId.Equals(email, StringComparison.OrdinalIgnoreCase));

            // Should be ordered by SentDateTime descending
            var resultList = result.ToList();
            resultList.Should().BeInDescendingOrder(m => m.SentDateTime);
        }

        #endregion

        #region GetReceivedMessages Tests

        [Test]
        public async Task GetReceivedMessages_ValidEmail_ReturnsReceivedMessages()
        {
            // Arrange
            var email = "<EMAIL>";

            _messageRepositoryMock.Setup(x => x.GetAsync())
                .ReturnsAsync(_testMessages);

            // Act
            var result = await _queryHandler.GetReceivedMessages(email);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2); // Two messages <NAME_EMAIL>
            result.Should().OnlyContain(m => m.ReceiverEmailId.Equals(email, StringComparison.OrdinalIgnoreCase));

            // Should be ordered by SentDateTime descending
            var resultList = result.ToList();
            resultList.Should().BeInDescendingOrder(m => m.SentDateTime);
        }

        #endregion

        #region GetDeletedMessagesByEmail Tests

        [Test]
        public async Task GetDeletedMessagesByEmail_ValidEmail_ReturnsDeletedMessages()
        {
            // Arrange
            var email = "<EMAIL>";
            var deletedMessage = _testMessages[0];
            deletedMessage.IsDeleted = true;
            deletedMessage.DeletedDateTime = DateTime.UtcNow;

            _messageRepositoryMock.Setup(x => x.GetAsync())
                .ReturnsAsync(_testMessages);

            // Act
            var result = await _queryHandler.GetDeletedMessagesByEmail(email);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1); // Only one deleted <NAME_EMAIL>
            result.Should().OnlyContain(m => m.IsDeleted);
            result.Should().OnlyContain(m =>
                m.SenderEmailId.Equals(email, StringComparison.OrdinalIgnoreCase) ||
                m.ReceiverEmailId.Equals(email, StringComparison.OrdinalIgnoreCase));
        }

        #endregion
    }
}
