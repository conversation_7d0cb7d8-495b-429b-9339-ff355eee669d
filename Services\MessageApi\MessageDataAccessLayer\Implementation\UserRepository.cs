using DataAccessLayer.Context;
using MessageContracts;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;

namespace MessageDataAccessLayer.Implementation
{
    public class UserRepository : GenericRepository<User>, IUserRepository
    {
        private readonly MessageApiDatabaseContext _context;
        public UserRepository(MessageApiDatabaseContext context) : base(context)
        {
            _context = context;
        }

        
    }
}
