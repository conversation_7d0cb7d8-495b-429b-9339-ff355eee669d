﻿using System;
using System.Collections.Generic;

namespace MessageContracts
{
    public class Conversation : IContract
    {
        public string ConversationId { get; set; }
        public Guid OrgId { get; set; }             
        public string? Name { get; set; }             
        public bool IsGroup { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public Guid CreatedByUserId { get; set; }

        public virtual ICollection<ConversationParticipant> Participants { get; set; }
            = new List<ConversationParticipant>();

        public virtual ICollection<Message> Messages { get; set; }
            = new List<Message>();
    }
}
