﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MessageDataAccessLayer.Implementation
{
    public interface IUnitOfWork : IDisposable
    {
        IMessageRepository MessageRepository { get; }
        IAttachmentRepository AttachmentRepository { get; }
        IUserRepository UserRepository { get; }
        IConversationRepository ConversationRepository { get; }
        IMessageReceiptsRepository MessageReceiptsRepository { get; }
        Task<int> SaveAsync();
    }
}
