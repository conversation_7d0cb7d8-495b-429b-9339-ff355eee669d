﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace MemberServiceApi.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Strings {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Strings() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("MemberServiceApi.Resources.Strings", typeof(Strings).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ProductId and MemberId are required..
        /// </summary>
        public static string AccessError {
            get {
                return ResourceManager.GetString("AccessError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access record not found.
        /// </summary>
        public static string AccessNotFound {
            get {
                return ResourceManager.GetString("AccessNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access updated successfully.
        /// </summary>
        public static string AccessUpdateSuccessful {
            get {
                return ResourceManager.GetString("AccessUpdateSuccessful", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Accounts.
        /// </summary>
        public static string Accounts {
            get {
                return ResourceManager.GetString("Accounts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AccountService.
        /// </summary>
        public static string AccountService {
            get {
                return ResourceManager.GetString("AccountService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AddingNewPlanType.
        /// </summary>
        public static string AddingNewPlanType {
            get {
                return ResourceManager.GetString("AddingNewPlanType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AddingNewPreDefinedPageRoleMapping.
        /// </summary>
        public static string AddingNewPreDefinedPageRoleMapping {
            get {
                return ResourceManager.GetString("AddingNewPreDefinedPageRoleMapping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AddingNewUserLicense.
        /// </summary>
        public static string AddingNewUserLicense {
            get {
                return ResourceManager.GetString("AddingNewUserLicense", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AllPlanTypesFetchedSuccessfully.
        /// </summary>
        public static string AllPlanTypesFetchedSuccessfully {
            get {
                return ResourceManager.GetString("AllPlanTypesFetchedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AllUserLicensesFetchedSuccessfully.
        /// </summary>
        public static string AllUserLicensesFetchedSuccessfully {
            get {
                return ResourceManager.GetString("AllUserLicensesFetchedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A database error occurred..
        /// </summary>
        public static string DatabaseError {
            get {
                return ResourceManager.GetString("DatabaseError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item is not deleted.
        /// </summary>
        public static string DeleteLogError {
            get {
                return ResourceManager.GetString("DeleteLogError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Successful.
        /// </summary>
        public static string DeleteSuccessful {
            get {
                return ResourceManager.GetString("DeleteSuccessful", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DeletingPlanTypeWithID.
        /// </summary>
        public static string DeletingPlanTypeWithID {
            get {
                return ResourceManager.GetString("DeletingPlanTypeWithID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DeletingPreDefinedPageRoleMappingWithID.
        /// </summary>
        public static string DeletingPreDefinedPageRoleMappingWithID {
            get {
                return ResourceManager.GetString("DeletingPreDefinedPageRoleMappingWithID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DeletingUserLicenseWithID.
        /// </summary>
        public static string DeletingUserLicenseWithID {
            get {
                return ResourceManager.GetString("DeletingUserLicenseWithID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorAddingPlanType.
        /// </summary>
        public static string ErrorAddingPlanType {
            get {
                return ResourceManager.GetString("ErrorAddingPlanType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorAddingPreDefinedPageRoleMapping.
        /// </summary>
        public static string ErrorAddingPreDefinedPageRoleMapping {
            get {
                return ResourceManager.GetString("ErrorAddingPreDefinedPageRoleMapping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorAddingUserLicense.
        /// </summary>
        public static string ErrorAddingUserLicense {
            get {
                return ResourceManager.GetString("ErrorAddingUserLicense", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorDeletingPlanType.
        /// </summary>
        public static string ErrorDeletingPlanType {
            get {
                return ResourceManager.GetString("ErrorDeletingPlanType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorDeletingPreDefinedPageRoleMapping.
        /// </summary>
        public static string ErrorDeletingPreDefinedPageRoleMapping {
            get {
                return ResourceManager.GetString("ErrorDeletingPreDefinedPageRoleMapping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorDeletingUserLicense.
        /// </summary>
        public static string ErrorDeletingUserLicense {
            get {
                return ResourceManager.GetString("ErrorDeletingUserLicense", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingPages.
        /// </summary>
        public static string ErrorFetchingPages {
            get {
                return ResourceManager.GetString("ErrorFetchingPages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingPlanType.
        /// </summary>
        public static string ErrorFetchingPlanType {
            get {
                return ResourceManager.GetString("ErrorFetchingPlanType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingPlanTypes.
        /// </summary>
        public static string ErrorFetchingPlanTypes {
            get {
                return ResourceManager.GetString("ErrorFetchingPlanTypes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingPreDefinedPageRoleMapping.
        /// </summary>
        public static string ErrorFetchingPreDefinedPageRoleMapping {
            get {
                return ResourceManager.GetString("ErrorFetchingPreDefinedPageRoleMapping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingUserLicense.
        /// </summary>
        public static string ErrorFetchingUserLicense {
            get {
                return ResourceManager.GetString("ErrorFetchingUserLicense", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorSearchingPreDefinedPageRoleMappings.
        /// </summary>
        public static string ErrorSearchingPreDefinedPageRoleMappings {
            get {
                return ResourceManager.GetString("ErrorSearchingPreDefinedPageRoleMappings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorUpdatingPlanType.
        /// </summary>
        public static string ErrorUpdatingPlanType {
            get {
                return ResourceManager.GetString("ErrorUpdatingPlanType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorUpdatingPreDefinedPageRoleMapping.
        /// </summary>
        public static string ErrorUpdatingPreDefinedPageRoleMapping {
            get {
                return ResourceManager.GetString("ErrorUpdatingPreDefinedPageRoleMapping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorUpdatingUserLicense.
        /// </summary>
        public static string ErrorUpdatingUserLicense {
            get {
                return ResourceManager.GetString("ErrorUpdatingUserLicense", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FetchingAllPlanTypes.
        /// </summary>
        public static string FetchingAllPlanTypes {
            get {
                return ResourceManager.GetString("FetchingAllPlanTypes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FetchingAllPreDefinedPageRoleMappings.
        /// </summary>
        public static string FetchingAllPreDefinedPageRoleMappings {
            get {
                return ResourceManager.GetString("FetchingAllPreDefinedPageRoleMappings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FetchingAllUserLicenses.
        /// </summary>
        public static string FetchingAllUserLicenses {
            get {
                return ResourceManager.GetString("FetchingAllUserLicenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FetchingPagesWithRoleID.
        /// </summary>
        public static string FetchingPagesWithRoleID {
            get {
                return ResourceManager.GetString("FetchingPagesWithRoleID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FetchingPlanTypeWithID.
        /// </summary>
        public static string FetchingPlanTypeWithID {
            get {
                return ResourceManager.GetString("FetchingPlanTypeWithID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FetchingPreDefinedPageRoleMappingWithID.
        /// </summary>
        public static string FetchingPreDefinedPageRoleMappingWithID {
            get {
                return ResourceManager.GetString("FetchingPreDefinedPageRoleMappingWithID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FetchingUserLicenseWithID.
        /// </summary>
        public static string FetchingUserLicenseWithID {
            get {
                return ResourceManager.GetString("FetchingUserLicenseWithID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred while fetching members..
        /// </summary>
        public static string GetLogError {
            get {
                return ResourceManager.GetString("GetLogError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to InternalServerErrorMessage.
        /// </summary>
        public static string InternalServerErrorMessage {
            get {
                return ResourceManager.GetString("InternalServerErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to InvalidDataForUpdate.
        /// </summary>
        public static string InvalidDataForUpdate {
            get {
                return ResourceManager.GetString("InvalidDataForUpdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The given Id is not valid.
        /// </summary>
        public static string InvalidId {
            get {
                return ResourceManager.GetString("InvalidId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not a valid member.
        /// </summary>
        public static string InvalidMember {
            get {
                return ResourceManager.GetString("InvalidMember", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to InvalidPlanTypeData.
        /// </summary>
        public static string InvalidPlanTypeData {
            get {
                return ResourceManager.GetString("InvalidPlanTypeData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to InvalidPreDefinedPageRoleMappingData.
        /// </summary>
        public static string InvalidPreDefinedPageRoleMappingData {
            get {
                return ResourceManager.GetString("InvalidPreDefinedPageRoleMappingData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to InvalidUserLicenseData.
        /// </summary>
        public static string InvalidUserLicenseData {
            get {
                return ResourceManager.GetString("InvalidUserLicenseData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Licenses.
        /// </summary>
        public static string Licenses {
            get {
                return ResourceManager.GetString("Licenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Member Not Found.
        /// </summary>
        public static string MemberNotFound {
            get {
                return ResourceManager.GetString("MemberNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NEWID().
        /// </summary>
        public static string NewIdFunction {
            get {
                return ResourceManager.GetString("NewIdFunction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No members to register..
        /// </summary>
        public static string NoMembers {
            get {
                return ResourceManager.GetString("NoMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NoPlanTypesFound.
        /// </summary>
        public static string NoPlanTypesFound {
            get {
                return ResourceManager.GetString("NoPlanTypesFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NoPreDefinedPageRoleMappingsFound.
        /// </summary>
        public static string NoPreDefinedPageRoleMappingsFound {
            get {
                return ResourceManager.GetString("NoPreDefinedPageRoleMappingsFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NoPreDefinedPageRoleMappingsFoundByPagePath.
        /// </summary>
        public static string NoPreDefinedPageRoleMappingsFoundByPagePath {
            get {
                return ResourceManager.GetString("NoPreDefinedPageRoleMappingsFoundByPagePath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PagePathRequired.
        /// </summary>
        public static string PagePathRequired {
            get {
                return ResourceManager.GetString("PagePathRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PagePathRequiredMessage.
        /// </summary>
        public static string PagePathRequiredMessage {
            get {
                return ResourceManager.GetString("PagePathRequiredMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PagesFetchedSuccessfully.
        /// </summary>
        public static string PagesFetchedSuccessfully {
            get {
                return ResourceManager.GetString("PagesFetchedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PagesNotFound.
        /// </summary>
        public static string PagesNotFound {
            get {
                return ResourceManager.GetString("PagesNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PlanTypeAddedSuccessfully.
        /// </summary>
        public static string PlanTypeAddedSuccessfully {
            get {
                return ResourceManager.GetString("PlanTypeAddedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PlanTypeDeletedSuccessfully.
        /// </summary>
        public static string PlanTypeDeletedSuccessfully {
            get {
                return ResourceManager.GetString("PlanTypeDeletedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PlanTypeFetchedSuccessfully.
        /// </summary>
        public static string PlanTypeFetchedSuccessfully {
            get {
                return ResourceManager.GetString("PlanTypeFetchedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PlanTypeInvalidMessage.
        /// </summary>
        public static string PlanTypeInvalidMessage {
            get {
                return ResourceManager.GetString("PlanTypeInvalidMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PlanTypeNotFound.
        /// </summary>
        public static string PlanTypeNotFound {
            get {
                return ResourceManager.GetString("PlanTypeNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PlanTypeNotFoundForDeletion.
        /// </summary>
        public static string PlanTypeNotFoundForDeletion {
            get {
                return ResourceManager.GetString("PlanTypeNotFoundForDeletion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PlanTypeNotFoundForUpdate.
        /// </summary>
        public static string PlanTypeNotFoundForUpdate {
            get {
                return ResourceManager.GetString("PlanTypeNotFoundForUpdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PlanTypeNotFoundMessage.
        /// </summary>
        public static string PlanTypeNotFoundMessage {
            get {
                return ResourceManager.GetString("PlanTypeNotFoundMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PlanTypesNotFoundMessage.
        /// </summary>
        public static string PlanTypesNotFoundMessage {
            get {
                return ResourceManager.GetString("PlanTypesNotFoundMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PlanTypeUpdatedSuccessfully.
        /// </summary>
        public static string PlanTypeUpdatedSuccessfully {
            get {
                return ResourceManager.GetString("PlanTypeUpdatedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred while registering members..
        /// </summary>
        public static string PostLogError {
            get {
                return ResourceManager.GetString("PostLogError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PreDefinedPageRoleMappingAddedSuccessfully.
        /// </summary>
        public static string PreDefinedPageRoleMappingAddedSuccessfully {
            get {
                return ResourceManager.GetString("PreDefinedPageRoleMappingAddedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PreDefinedPageRoleMappingDeletedSuccessfully.
        /// </summary>
        public static string PreDefinedPageRoleMappingDeletedSuccessfully {
            get {
                return ResourceManager.GetString("PreDefinedPageRoleMappingDeletedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PreDefinedPageRoleMappingFetchedSuccessfully.
        /// </summary>
        public static string PreDefinedPageRoleMappingFetchedSuccessfully {
            get {
                return ResourceManager.GetString("PreDefinedPageRoleMappingFetchedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PreDefinedPageRoleMappingInvalidMessage.
        /// </summary>
        public static string PreDefinedPageRoleMappingInvalidMessage {
            get {
                return ResourceManager.GetString("PreDefinedPageRoleMappingInvalidMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PreDefinedPageRoleMappingNotFound.
        /// </summary>
        public static string PreDefinedPageRoleMappingNotFound {
            get {
                return ResourceManager.GetString("PreDefinedPageRoleMappingNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PreDefinedPageRoleMappingNotFoundForDeletion.
        /// </summary>
        public static string PreDefinedPageRoleMappingNotFoundForDeletion {
            get {
                return ResourceManager.GetString("PreDefinedPageRoleMappingNotFoundForDeletion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PreDefinedPageRoleMappingNotFoundForUpdate.
        /// </summary>
        public static string PreDefinedPageRoleMappingNotFoundForUpdate {
            get {
                return ResourceManager.GetString("PreDefinedPageRoleMappingNotFoundForUpdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PreDefinedPageRoleMappingNotFoundMessage.
        /// </summary>
        public static string PreDefinedPageRoleMappingNotFoundMessage {
            get {
                return ResourceManager.GetString("PreDefinedPageRoleMappingNotFoundMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PreDefinedPageRoleMappingsFetchedSuccessfully.
        /// </summary>
        public static string PreDefinedPageRoleMappingsFetchedSuccessfully {
            get {
                return ResourceManager.GetString("PreDefinedPageRoleMappingsFetchedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PreDefinedPageRoleMappingsNotFoundMessage.
        /// </summary>
        public static string PreDefinedPageRoleMappingsNotFoundMessage {
            get {
                return ResourceManager.GetString("PreDefinedPageRoleMappingsNotFoundMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PreDefinedPageRoleMappingUpdatedSuccessfully.
        /// </summary>
        public static string PreDefinedPageRoleMappingUpdatedSuccessfully {
            get {
                return ResourceManager.GetString("PreDefinedPageRoleMappingUpdatedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Products.
        /// </summary>
        public static string Products {
            get {
                return ResourceManager.GetString("Products", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ProductService.
        /// </summary>
        public static string ProductService {
            get {
                return ResourceManager.GetString("ProductService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ProductUserAccess.
        /// </summary>
        public static string ProductUserAccess {
            get {
                return ResourceManager.GetString("ProductUserAccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to registration.
        /// </summary>
        public static string registration {
            get {
                return ResourceManager.GetString("registration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SearchingPreDefinedPageRoleMappingsByPagePath.
        /// </summary>
        public static string SearchingPreDefinedPageRoleMappingsByPagePath {
            get {
                return ResourceManager.GetString("SearchingPreDefinedPageRoleMappingsByPagePath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Members registered successfully..
        /// </summary>
        public static string SuccessfulRegistration {
            get {
                return ResourceManager.GetString("SuccessfulRegistration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log not updated.
        /// </summary>
        public static string UpdateLogError {
            get {
                return ResourceManager.GetString("UpdateLogError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update Successful.
        /// </summary>
        public static string UpdateSuccessful {
            get {
                return ResourceManager.GetString("UpdateSuccessful", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UpdatingPlanTypeWithID.
        /// </summary>
        public static string UpdatingPlanTypeWithID {
            get {
                return ResourceManager.GetString("UpdatingPlanTypeWithID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UpdatingPreDefinedPageRoleMappingWithID.
        /// </summary>
        public static string UpdatingPreDefinedPageRoleMappingWithID {
            get {
                return ResourceManager.GetString("UpdatingPreDefinedPageRoleMappingWithID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UpdatingUserLicenseWithID.
        /// </summary>
        public static string UpdatingUserLicenseWithID {
            get {
                return ResourceManager.GetString("UpdatingUserLicenseWithID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserLicenseAddedSuccessfully.
        /// </summary>
        public static string UserLicenseAddedSuccessfully {
            get {
                return ResourceManager.GetString("UserLicenseAddedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserLicenseDeletedSuccessfully.
        /// </summary>
        public static string UserLicenseDeletedSuccessfully {
            get {
                return ResourceManager.GetString("UserLicenseDeletedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserLicenseFetchedSuccessfully.
        /// </summary>
        public static string UserLicenseFetchedSuccessfully {
            get {
                return ResourceManager.GetString("UserLicenseFetchedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserLicenseInvalidMessage.
        /// </summary>
        public static string UserLicenseInvalidMessage {
            get {
                return ResourceManager.GetString("UserLicenseInvalidMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserLicenseNotFound.
        /// </summary>
        public static string UserLicenseNotFound {
            get {
                return ResourceManager.GetString("UserLicenseNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserLicenseNotFoundForDeletion.
        /// </summary>
        public static string UserLicenseNotFoundForDeletion {
            get {
                return ResourceManager.GetString("UserLicenseNotFoundForDeletion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserLicenseNotFoundForUpdate.
        /// </summary>
        public static string UserLicenseNotFoundForUpdate {
            get {
                return ResourceManager.GetString("UserLicenseNotFoundForUpdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserLicenseNotFoundMessage.
        /// </summary>
        public static string UserLicenseNotFoundMessage {
            get {
                return ResourceManager.GetString("UserLicenseNotFoundMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserLicenseUpdatedSuccessfully.
        /// </summary>
        public static string UserLicenseUpdatedSuccessfully {
            get {
                return ResourceManager.GetString("UserLicenseUpdatedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Users.
        /// </summary>
        public static string Users {
            get {
                return ResourceManager.GetString("Users", resourceCulture);
            }
        }
    }
}
