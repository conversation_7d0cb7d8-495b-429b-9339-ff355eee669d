using MessageContracts;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MessageBusinessLayer
{
    public interface IMessageCommandHandler<T>
    {
        Task<Message> SendMessageAsync(Message message);
        Task<Message> UpdateMessageAsync(Guid messageId, string newContent);
        Task DeleteMessagesAsync(IEnumerable<Guid> messageIds);
        Task<IEnumerable<MessageAttachment>> UpdateMessageAttachmentsAsync(
            Guid messageId,
            IEnumerable<(Guid? AttachmentId, byte[] Content, string FileName, string ContentType)> files);
        Task MarkMessageAsReadAsync(Guid messageId, Guid userId);
    }
}
