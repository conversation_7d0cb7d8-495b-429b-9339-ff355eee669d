using System;
using System.Collections.Generic;

namespace MessageContracts
{
    public class Message : IContract
    {
        public Guid MessageId { get; set; }
        public Guid ConversationId { get; set; }
        public Guid SenderUserId { get; set; }

        public string MessageContent { get; set; } = string.Empty;
        public bool HasAttachments { get; set; } = false;

        public DateTime SentDateTime { get; set; } = DateTime.UtcNow;
        public bool IsDeleted { get; set; } = false;
        public DateTime? DeletedDateTime { get; set; }

        // Navigation
        public virtual Conversation Conversation { get; set; }
        public virtual User Sender { get; set; }
        public virtual ICollection<MessageAttachment> Attachments { get; set; }
            = new List<MessageAttachment>();
        public virtual ICollection<MessageReceipt> Receipts { get; set; }
            = new List<MessageReceipt>();
    }
}
