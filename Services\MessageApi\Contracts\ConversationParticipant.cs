﻿using System;

namespace MessageContracts
{
    public class ConversationParticipant
    {
        public Guid ConversationParticipantId { get; set; }
        public string ConversationId { get; set; }
        public Guid UserId { get; set; }

        public bool IsAdmin { get; set; } = false;   // For group control
        public DateTime JoinedAt { get; set; } = DateTime.UtcNow;

        // Per-user state
        public DateTime? LastReadAt { get; set; }

        // Navigation
        public virtual Conversation Conversation { get; set; }
        public virtual User User { get; set; }
    }
}
