using MessageContracts;
using MessageBusinessLayer;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MessageApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MessageController : ControllerBase
    {
        private readonly IMessageCommandHandler<Message> _messageCommandHandler;
        private readonly IMessageQueryHandler<Message> _messageQueryHandler;
        private readonly ILogger<MessageController> _logger;
        private readonly IStringLocalizer<MessageController> _localizer;

        public MessageController(
            IMessageCommandHandler<Message> messageCommandHandler,
            IMessageQueryHandler<Message> messageQueryHandler,
            ILogger<MessageController> logger,
            IStringLocalizer<MessageController> localizer
        )
        {
            _messageCommandHandler = messageCommandHandler;
            _messageQueryHandler = messageQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        // POST: api/Message
        [HttpPost("send")]
        public async Task<IActionResult> SendMessage([FromBody] Message message)
        {
            try
            {
                var result = await _messageCommandHandler.SendMessageAsync(message);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending message in conversation {ConversationId}", message.ConversationId);
                return StatusCode(500, "Failed to send message.");
            }
        }

        // PUT: api/Message/{messageId}
        [HttpPut("{messageId}")]
        public async Task<IActionResult> UpdateMessage(Guid messageId, [FromBody] string newContent)
        {
            try
            {
                var updatedMessage = await _messageCommandHandler.UpdateMessageAsync(messageId, newContent);
                if (updatedMessage == null)
                    return NotFound($"Message {messageId} not found.");

                return Ok(updatedMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating message {MessageId}", messageId);
                return StatusCode(500, "Failed to update message.");
            }
        }

        // DELETE: api/Message
        [HttpDelete]
        public async Task<IActionResult> DeleteMessages([FromBody] IEnumerable<Guid> messageIds)
        {
            try
            {
                await _messageCommandHandler.DeleteMessagesAsync(messageIds);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting messages");
                return StatusCode(500, "Failed to delete messages.");
            }
        }

        // POST: api/Message/{messageId}/attachments
        [HttpPost("{messageId}/attachments")]
        public async Task<IActionResult> UpdateAttachments(Guid messageId, [FromBody] IEnumerable<(Guid? AttachmentId, byte[] Content, string FileName, string ContentType)> files)
        {
            try
            {
                var attachments = await _messageCommandHandler.UpdateMessageAttachmentsAsync(messageId, files);
                return Ok(attachments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating attachments for message {MessageId}", messageId);
                return StatusCode(500, "Failed to update attachments.");
            }
        }

        // POST: api/Message/{messageId}/read/{userId}
        [HttpPost("{messageId}/read/{userId}")]
        public async Task<IActionResult> MarkAsRead(Guid messageId, Guid userId)
        {
            try
            {
                await _messageCommandHandler.MarkMessageAsReadAsync(messageId, userId);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking message {MessageId} as read by user {UserId}", messageId, userId);
                return StatusCode(500, "Failed to mark message as read.");
            }
        }

        // GET: api/Message/conversation/{conversationId}
        [HttpGet("conversation/{conversationId}")]
        public async Task<IActionResult> GetMessagesByConversation(Guid conversationId, [FromQuery] int page = 1, [FromQuery] int pageSize = 50)
        {
            try
            {
                var messages = await _messageQueryHandler.GetMessagesByConversationAsync(conversationId, page, pageSize);
                return Ok(messages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching messages for conversation {ConversationId}", conversationId);
                return StatusCode(500, "Failed to fetch messages.");
            }
        }

        // GET: api/Message/{messageId}
        [HttpGet("{messageId}")]
        public async Task<IActionResult> GetMessageById(Guid messageId)
        {
            try
            {
                var message = await _messageQueryHandler.GetMessageByIdAsync(messageId);
                if (message == null) return NotFound($"Message {messageId} not found.");
                return Ok(message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching message {MessageId}", messageId);
                return StatusCode(500, "Failed to fetch message.");
            }
        }

        // GET: api/Message/attachment/{attachmentId}
        [HttpGet("attachment/{attachmentId}")]
        public async Task<IActionResult> GetAttachment(Guid attachmentId)
        {
            try
            {
                var attachment = await _messageQueryHandler.GetMessageAttachmentAsync(attachmentId);
                if (attachment == null) return NotFound($"Attachment {attachmentId} not found.");
                return Ok(attachment);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching attachment {AttachmentId}", attachmentId);
                return StatusCode(500, "Failed to fetch attachment.");
            }
        }

        // GET: api/Message/attachment/download/{attachmentId}
        [HttpGet("attachment/download/{attachmentId}")]
        public async Task<IActionResult> DownloadAttachment(Guid attachmentId)
        {
            try
            {
                var data = await _messageQueryHandler.DownloadAttachmentAsync(attachmentId);
                var attachment = await _messageQueryHandler.GetMessageAttachmentAsync(attachmentId);
                if (attachment == null) return NotFound($"Attachment {attachmentId} not found.");

                return File(data, attachment.ContentType, attachment.FileName);
            }
            catch (FileNotFoundException fnf)
            {
                return NotFound(fnf.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading attachment {AttachmentId}", attachmentId);
                return StatusCode(500, "Failed to download attachment.");
            }
        }
    }
}
