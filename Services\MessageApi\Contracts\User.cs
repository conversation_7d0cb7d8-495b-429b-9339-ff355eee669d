using System;

namespace MessageContracts
{
    public class User : IContract
    {
        public Guid UserId { get; set; }
        public Guid OrgId { get; set; }               // Tenant for SaaS
        public string Name { get; set; }
        public string? AcsUserId { get; set; }        // ACS user identity

        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedDate { get; set; } = DateTime.UtcNow;
        public bool IsActive { get; set; } = false;
    }
}
