using DataAccessLayer.Context;
using MessageContracts;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MessageDataAccessLayer.Implementation
{
    public class MessageRepository : GenericRepository<Message>, IMessageRepository
    {
        private readonly MessageApiDatabaseContext _context;
        public MessageRepository(MessageApiDatabaseContext context) : base(context)
        {
            _context = context;
        }

        
    }
}
