﻿using MessageContracts;
using MessageDataAccessLayer.Implementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MessageBusinessLayer.QueryHandler
{
    public class UserQueryHandler : IUserQueryHandler<User> 
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<UserQueryHandler> _logger;
        public UserQueryHandler(
           IConfiguration configuration,
           IUnitOfWork unitOfWork,
           ILogger<UserQueryHandler> logger)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<User?> GetAcsUserById(Guid userId, Guid orgId)
        {
            var user = await _unitOfWork.UserRepository.GetByIdAsync(userId);

            if (user == null)
                return null;

            if (user.OrgId != orgId || !user.IsActive)
                return null;

            return user;
        }

    }


}
