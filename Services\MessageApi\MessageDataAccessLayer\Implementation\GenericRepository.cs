﻿using DataAccessLayer.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MessageDataAccessLayer.Implementation
{
    public class GenericRepository<T> : IGenericRepository<T> where T : class
    {
       protected readonly MessageApiDatabaseContext _context;
        private readonly DbSet<T> _dbSet;

        public GenericRepository(MessageApiDatabaseContext context)
        {
            _context = context;
            _dbSet = context.Set<T>();
        }


        // Queryable for custom filtering
        public IQueryable<T> Query()
        {
            return _dbSet.AsQueryable();
        }

        // Insert single entity
        public async Task AddAsync(T entity)
        {
            try
            {
                await _dbSet.AddAsync(entity);
            }
            catch (Exception ex)
            {
                var errorMessage = ex.InnerException?.Message ?? ex.Message;
                throw new InvalidOperationException($"Error adding entity: {errorMessage}", ex);
            }
        }

        // Insert multiple entities
        public async Task AddAsync(IEnumerable<T> entities)
        {
            try
            {
                await _dbSet.AddRangeAsync(entities);
            }
            catch (Exception ex)
            {
                var errorMessage = ex.InnerException?.Message ?? ex.Message;
                throw new InvalidOperationException($"Error adding entities: {errorMessage}", ex);
            }
        }

        public async Task<IEnumerable<T>> GetAllAsync() => await _dbSet.ToListAsync();
        public async Task<IEnumerable<T>> GetAsync() => await _dbSet.ToListAsync();

        public async Task<IEnumerable<T>> GetAsync(bool includeDeleted)
        {
            if (includeDeleted)
                return await _dbSet.IgnoreQueryFilters().ToListAsync();
            return await _dbSet.ToListAsync();
        }

        public async Task<T> GetByIdAsync(Guid id) => await _dbSet.FindAsync(id);

        public async Task<T> GetByIdAsync(Guid id, bool includeDeleted)
        {
            if (includeDeleted)
            {
                return await _dbSet.IgnoreQueryFilters()
                    .FirstOrDefaultAsync(e => EF.Property<Guid>(e, "Id") == id);
            }
            return await _dbSet.FindAsync(id);
        }

        public async Task UpdateAsync(T entity)
        {
            _dbSet.Update(entity);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateRangeAsync(IEnumerable<T> entities)
        {
            _context.Set<T>().UpdateRange(entities);
            await Task.CompletedTask;
        }

        public async Task DeleteByEntityAsync(T entity)
        {
            _dbSet.Remove(entity);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteByIdAsync(Guid id)
        {
            var entity = await GetByIdAsync(id);
            if (entity != null)
                await DeleteByEntityAsync(entity);
        }

        public IQueryable<T> Query(bool asNoTracking = true, bool includeDeleted = false)
        {
            IQueryable<T> query = _dbSet;

            if (asNoTracking)
                query = query.AsNoTracking();

            if (includeDeleted)
                query = query.IgnoreQueryFilters();

            return query;
        }

    }
}
